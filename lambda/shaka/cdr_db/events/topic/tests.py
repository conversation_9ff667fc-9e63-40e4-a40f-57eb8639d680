import datetime
import json
from unittest import mock

import attrs
import time_machine
from django.test import TestCase

from . import topic


@attrs.frozen
class _TestEvent:
    some_field: str

@attrs.frozen
class _TestOtherEvent:
    some_other_field: str


class NoOpPublisher(topic.Publisher):
    def publish(self, unstructured_event: topic.UnstructuredEvent) -> None:
        pass


class TopicTests__EventHandling(TestCase):
    def test_event_handler_is_called_for_event(self):
        mock_handler = mock.Mock()

        events_handlers = [
            topic.EventHandlers(_TestEvent, (mock_handler,))
        ]
        _topic = topic.Topic(events_handlers=events_handlers, publisher=NoOpPublisher())
        _topic.handle({"__type": "_TestEvent", "some_field": "some-value"})

        mock_handler.assert_called_once_with(_TestEvent(some_field="some-value"))

    def test_dispatches_to_correct_handler(self):
        mock_correct_handler = mock.Mock()
        mock_incorrect_handler = mock.Mock()

        events_handlers = [
            topic.EventHandlers(_TestEvent, (mock_correct_handler,)),
            topic.EventHandlers(_TestOtherEvent, (mock_incorrect_handler,))
        ]
        _topic = topic.Topic(events_handlers=events_handlers, publisher=NoOpPublisher())
        _topic.handle({"__type": "_TestEvent", "some_field": "some-value"})

        mock_correct_handler.assert_called_once_with(_TestEvent(some_field="some-value"))
        mock_incorrect_handler.assert_not_called()

    def test_executes_multiple_handlers(self):
        mock_correct_handler_one = mock.Mock()
        mock_correct_handler_two = mock.Mock()
        mock_incorrect_handler = mock.Mock()

        events_handlers = [
            topic.EventHandlers(_TestEvent, (mock_correct_handler_one, mock_correct_handler_two)),
            topic.EventHandlers(_TestOtherEvent, (mock_incorrect_handler,))
        ]
        _topic = topic.Topic(events_handlers=events_handlers, publisher=NoOpPublisher())
        _topic.handle({"__type": "_TestEvent", "some_field": "some-value"})

        mock_correct_handler_one.assert_called_once_with(_TestEvent(some_field="some-value"))
        mock_correct_handler_two.assert_called_once_with(_TestEvent(some_field="some-value"))
        mock_incorrect_handler.assert_not_called()

    def test_erroring_handler_doesnt_stop_execution(self):
        mock_erroring_handler = mock.Mock()
        mock_erroring_handler.side_effect = Exception("Some error")
        mock_handler = mock.Mock()

        events_handlers = [
            topic.EventHandlers(_TestEvent, (mock_erroring_handler, mock_handler)),
        ]
        _topic = topic.Topic(events_handlers=events_handlers, publisher=NoOpPublisher())
        _topic.handle({"__type": "_TestEvent", "some_field": "some-value"})

        mock_erroring_handler.assert_called_once_with(_TestEvent(some_field="some-value"))
        mock_handler.assert_called_once_with(_TestEvent(some_field="some-value"))

    def test_raises_when_handler_is_missing(self):
        _topic = topic.Topic(events_handlers=[], publisher=NoOpPublisher())

        with self.assertRaises(topic.NoHandlerFoundError):
            _topic.handle({"__type": "_TestEvent", "some_field": "some-value"})

    def test_raises_when_event_is_garbage(self):
        _topic = topic.Topic(events_handlers=[], publisher=NoOpPublisher())

        with self.assertRaises(topic.MisconfiguredEventError):
            # No `__type` field
            _topic.handle({"some_field": "some-value"})


class SNSPublisherTests(TestCase):
    def test_can_publish_event_to_topic(self):
        mock_client = mock.Mock()

        class _TestPublisher(topic.SNSPublisher):
            def _get_client(self):
                return mock_client

        _topic = topic.Topic(events_handlers=[], publisher=_TestPublisher(arn="some-arn", subject="some-subject"))

        with time_machine.travel(datetime.datetime(2025, 1, 1)):
            _topic.publish(_TestEvent(some_field="some-value"))

        mock_client.publish.assert_called_once_with(
            TopicArn="some-arn",
            Message=json.dumps({"some_field": "some-value", "__type": "_TestEvent", "__published_at": 1735689600.0}),
            Subject="some-subject",
        )
