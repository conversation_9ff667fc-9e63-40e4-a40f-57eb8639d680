# Generated by Django 4.2.7 on 2024-12-08 18:45

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('provider', '0006_alter_provideraction_action'),
    ]

    operations = [
        migrations.CreateModel(
            name='Pollable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('polling_type', models.CharField(choices=[('service_provisioned', 'Service Provisioned'), ('service_activated', 'Service Activated'), ('service_on_plan', 'Service On Plan'), ('port', 'Port'), ('service_barred', 'Service Barred')], max_length=100)),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('completed', 'Completed'), ('errored', 'Errored'), ('timed_out', 'Timed Out')], default='in_progress', max_length=100)),
                ('last_updated', models.DateTimeField(default=django.utils.timezone.now)),
                ('poll_count', models.IntegerField(default=0)),
                ('error_count', models.IntegerField(default=0)),
                ('started_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('params', models.JSONField(blank=True, null=True)),
            ],
        ),
        migrations.AlterField(
            model_name='provideraction',
            name='action',
            field=models.CharField(choices=[('fake-bar-data-transatel', 'Fake Bar Data (Transatel)'), ('fake-plan-upgrade-transatel', 'Fake Plan Upgrade (Transatel)'), ('real-bar-data-transatel', 'Real Bar Data (Transatel)'), ('real-plan-upgrade-transatel', 'Real Plan Upgrade (Transatel)'), ('real-activate-sim-transatel', 'Real Activate Sim (Transatel)'), ('real-request-port-in-transatel', 'Real Request Port In (Transatel)'), ('gamma-provision-service', 'Gamma Provision Service'), ('gamma-activate-service', 'Gamma Activate Service'), ('gamma-change-plan', 'Gamma Change Plan'), ('gamma-request-port-in', 'Gamma Request Port In'), ('gamma-establish-capping', 'Gamma Establish Capping'), ('gamma-real-bar-data', 'Gamma Real Bar Data')], max_length=100),
        ),
    ]
