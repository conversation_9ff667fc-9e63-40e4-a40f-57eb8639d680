from rest_framework import serializers


class UsageSerializer(serializers.Serializer):
    msisdn = serializers.CharField(max_length=20)
    data_type = serializers.CharField(max_length=10)
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    total_usage = serializers.IntegerField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class AggregationSerializer(serializers.Serializer):
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    aggregation_period = serializers.ChoiceField(choices=['day', 'month', 'year'])
    total_usage = serializers.IntegerField()
    data_type = serializers.CharField(max_length=10)
    date = serializers.DateTimeField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError
