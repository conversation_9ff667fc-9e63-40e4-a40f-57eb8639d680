from typing import Generic, TypeVar, Callable, Iterable, Iterator

import attrs

from core.agnostic_cdr import events as agnostic_cdr_events
from core.provider_specific_cdr import events as provider_specific_cdr_events

T_AgnosticCDR = TypeVar("T_AgnosticCDR")
T_ProviderCDR = TypeVar("T_ProviderCDR")


@attrs.frozen
class Field:
    provider_specific_name: str
    agnostic_name: str
    converter: Callable


class ProviderToAgnosticConverter(Generic[T_ProviderCDR, T_AgnosticCDR]):
    fields: Iterable[str | Field]
    agnostic_cdr_type: type[T_AgnosticCDR]

    def __init__(self, fields: Iterable[str | Field], agnostic_cdr_type: type[T_AgnosticCDR]) -> None:
        self.fields = fields
        self.agnostic_cdr_type = agnostic_cdr_type

    def convert(self, provider_cdr: T_ProviderCDR) -> T_AgnosticCDR:
        converted_fields = {}
        for field in self._fields_to_convert():
            converted_fields[field.agnostic_name] = field.converter(getattr(provider_cdr, field.provider_specific_name))

        return self.agnostic_cdr_type(**converted_fields)

    def _fields_to_convert(self) -> Iterator[Field]:
        for field in self.fields:
            if isinstance(field, str):
                yield Field(field, field, lambda x: x)
            else:
                yield field


gamma_data_agnostic_converter: ProviderToAgnosticConverter[provider_specific_cdr_events.GammaDataCDREvent, agnostic_cdr_events.AgnosticDataCDREvent] = (
    ProviderToAgnosticConverter(
        fields=(
            "msisdn",
            "event_time",
            "data_usage_bytes",
            "roaming_zone",
        ),
        agnostic_cdr_type=agnostic_cdr_events.AgnosticDataCDREvent
    )
)
gamma_voice_agnostic_converter: ProviderToAgnosticConverter[provider_specific_cdr_events.GammaVoiceCDREvent, agnostic_cdr_events.AgnosticVoiceCDREvent] = (
    ProviderToAgnosticConverter(
        fields=(
            "msisdn",
            "event_time",
            "duration_seconds",
        ),
        agnostic_cdr_type=agnostic_cdr_events.AgnosticVoiceCDREvent
    )
)
gamma_sms_agnostic_converter: ProviderToAgnosticConverter[provider_specific_cdr_events.GammaSMSCDREvent, agnostic_cdr_events.AgnosticSMSCDREvent] = (
    ProviderToAgnosticConverter(
        fields=(
            "msisdn",
            "event_time",
            "quantity",
        ),
        agnostic_cdr_type=agnostic_cdr_events.AgnosticSMSCDREvent
    )
)
