import sys
import os
import json
import logging
from importlib import import_module

import tornado.httpserver
import tornado.ioloop
import tornado.options
import tornado.web

from tornado.options import define, options

current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '../nexus'))

define("port", default=8084, help="run on the given port", type=int)



class FakeSlackHandler(tornado.web.RequestHandler):
    def post(self):
        print('Slack:', json.loads(self.request.body)['text'])

@tornado.web.stream_request_body
class MainHandler(tornado.web.RequestHandler):
    def __init__(self, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.data = None
        self.path = None

    async def post(self, path):
        self.path = path
        if self.path and self.data:
            await self.process()

    def get_path_and_env(self, path):
        ___, _, name, __ = path.split('/')        
        return {
            'prod-slack-debug': ('lambda.slack_debug', {'SLACK_ENDPOINT': f'http://localhost:{options.port}/slack/'}),
            'prod-plan-change': ('lambda.plan_change', {'DJANGO_SETTINGS_MODULE': 'nexus.settings'}),
        }[name]

    def get_handler(self, path):
        module, env = self.get_path_and_env(path)
        os.environ.update(env)
        return getattr(import_module(module), 'lambda_handler')
    
    async def data_received(self, chunk):
        if self.data is None:
            self.data = chunk
        else:
            self.data += chunk
        if self.path and self.data:
            await self.process()

    async def process(self):
        json_data = json.loads(self.data.decode('utf-8'))
        lambda_handler = self.get_handler(self.path)
        response = await tornado.ioloop.IOLoop.current().run_in_executor(None, lambda: self.call_lambda(lambda_handler, json_data))
        self.write(json.dumps(response))

    def call_lambda(self, lambda_handler, json_data):
        print('invoking lambda', lambda_handler)
        try:
            response = lambda_handler(json_data, {})
            return response
        except Exception as e:
            print('Exc:', e, type(e))
            self.set_status(500)
            return {
    "errorType": type(e).__name__,
    "errorMessage": str(e) or type(e).__name__,
    "stack": [
    ]
}


def main():
    tornado.options.parse_command_line()

    application = tornado.web.Application([
        (r"/slack/", FakeSlackHandler),
        (r"/(.*)", MainHandler),
    ], autoreload=True)

    http_server = tornado.httpserver.HTTPServer(application)
    http_server.listen(options.port)

    tornado.ioloop.IOLoop.current().start()

if __name__ == "__main__":
    main()
