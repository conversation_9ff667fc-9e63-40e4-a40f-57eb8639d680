from django.db import models
from django.core.exceptions import ValidationError
from core.models import Plan, RoamingEsimPackage


class FamilyPlanLink(models.Model):
    main_plan = models.ForeignKey(
        Plan,
        on_delete=models.CASCADE,
        related_name='family_plan_links',
        help_text='The main plan that offers family plans'
    )
    sub_plan = models.ForeignKey(
        Plan,
        on_delete=models.CASCADE,
        related_name='as_family_plan_of',
        help_text='The plan that serves as the family plan'
    )
    price_override = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text='Override price for the family plan'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Whether this family plan link is currently active'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['main_plan', 'sub_plan']
        ordering = ['main_plan', 'sub_plan']

    def clean(self):
        if self.main_plan and self.sub_plan:
            if self.main_plan.client != self.sub_plan.client:
                raise ValidationError('Main plan and sub plan must belong to the same client')
            if self.main_plan == self.sub_plan:
                raise ValidationError('Main plan and sub plan cannot be the same')

    def __str__(self):
        status = "Active" if self.is_active else "Inactive"
        return f"Family Plan: {self.main_plan.name} → {self.sub_plan.name} (£{self.price_override}) [{self.main_plan.client.name}] - {status}"


class TravelAddon(models.Model):
    name = models.CharField(
        max_length=255,
        help_text='Name of the travel addon'
    )
    region = models.CharField(
        max_length=100,
        help_text='Region this addon covers (e.g., Europe, Asia)'
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text='Price for this travel addon'
    )
    roaming_esim_packages = models.ManyToManyField(
        RoamingEsimPackage,
        related_name='travel_addons',
        help_text='Roaming eSIM packages included in this addon'
    )
    plans = models.ManyToManyField(
        Plan,
        through='PlanTravelAddon',
        related_name='travel_addons',
        help_text='Plans that offer this travel addon'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Whether this travel addon is currently active'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['region', 'name']

    def __str__(self):
        status = "Active" if self.is_active else "Inactive"
        package_count = self.roaming_esim_packages.count() if self.pk else 0
        return f"Travel Addon: {self.name} | {self.region} | £{self.price} | {package_count} packages - {status}"

    @property
    def total_data_gb(self):
        return sum(package.data_limit_gb for package in self.roaming_esim_packages.all())


class PlanTravelAddon(models.Model):
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE)
    travel_addon = models.ForeignKey(TravelAddon, on_delete=models.CASCADE)
    price_override = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Plan-specific price override. If null, uses travel addon default price'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Whether this addon is available for this specific plan'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['plan', 'travel_addon']
        ordering = ['plan', 'travel_addon']

    def clean(self):
        if self.plan and self.travel_addon:
            plan_client = self.plan.client
            addon_packages = self.travel_addon.roaming_esim_packages.all()
            for package in addon_packages:
                if package.client != plan_client:
                    raise ValidationError(
                        f'Travel addon package {package.name} must belong to the same client as plan {self.plan.name}'
                    )

    @property
    def effective_price(self):
        return self.price_override if self.price_override is not None else self.travel_addon.price

    def __str__(self):
        price = self.effective_price
        status = "Active" if self.is_active else "Inactive"
        price_type = "Override" if self.price_override else "Default"
        return f"Plan Travel Addon: {self.plan.name} + {self.travel_addon.name} | £{price} ({price_type}) [{self.plan.client.name}] - {status}"
