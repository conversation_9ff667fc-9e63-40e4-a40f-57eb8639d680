from rest_framework import serializers
from .models import FamilyPlanLink, PlanTravelAddon


class TravelAddonSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    region = serializers.CharField()
    allowances = serializers.SerializerMethodField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)

    def create(self, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("TravelAddonSerializer is read-only")

    def update(self, instance, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("TravelAddonSerializer is read-only")

    def get_allowances(self, obj):
        travel_addon = obj.travel_addon if hasattr(obj, 'travel_addon') else obj
        total_data = travel_addon.total_data_gb

        return {
            "data": int(total_data) if total_data > 0 else "unlimited"
        }

    def to_representation(self, instance):
        if hasattr(instance, 'travel_addon'):
            travel_addon = instance.travel_addon
            price = instance.effective_price
        else:
            travel_addon = instance
            price = instance.price

        return {
            'id': travel_addon.id,
            'name': travel_addon.name,
            'region': travel_addon.region,
            'allowances': self.get_allowances(instance),
            'price': float(price)
        }


class FamilyPlanSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    allowances = serializers.SerializerMethodField()
    travel_addons = serializers.SerializerMethodField()

    def create(self, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("FamilyPlanSerializer is read-only")

    def update(self, instance, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("FamilyPlanSerializer is read-only")

    def get_allowances(self, obj):
        plan = obj.sub_plan
        data_display = plan.data_limit_display_qty
        if data_display == 'unlimited':
            data = 'unlimited'
        else:
            data = int(plan.data_limit_gb) if plan.data_limit_gb else 0

        voice_display = plan.voice_limit_display_qty
        if voice_display == 'unlimited':
            voice = 'unlimited'
        else:
            voice = int(plan.voice_limit) if plan.voice_limit else 0

        sms_display = plan.sms_limit_display_qty
        if sms_display == 'unlimited':
            texts = 'unlimited'
        else:
            texts = int(plan.sms_limit) if plan.sms_limit else 0

        return {
            "data": data,
            "calls": voice,
            "texts": texts,
            "europe_data": plan.base_eu_roaming_days
        }

    def get_travel_addons(self, obj):
        plan = obj.sub_plan

        plan_travel_addons = PlanTravelAddon.objects.filter(
            plan=plan,
            is_active=True,
            travel_addon__is_active=True
        ).select_related('travel_addon').prefetch_related('travel_addon__roaming_esim_packages')

        return TravelAddonSerializer(plan_travel_addons, many=True).data

    def to_representation(self, instance):
        return {
            'id': instance.sub_plan.id,
            'name': instance.sub_plan.name,
            'price': float(instance.price_override),
            'allowances': self.get_allowances(instance),
            'travel_addons': self.get_travel_addons(instance)
        }


class PlanSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    allowances = serializers.SerializerMethodField()
    travel_addons = serializers.SerializerMethodField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    family_plans = serializers.SerializerMethodField()

    def create(self, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("PlanSerializer is read-only")

    def update(self, instance, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("PlanSerializer is read-only")

    def get_allowances(self, obj):
        data_display = obj.data_limit_display_qty
        if data_display == 'unlimited':
            data = 'unlimited'
        else:
            data = int(obj.data_limit_gb) if obj.data_limit_gb else 0

        voice_display = obj.voice_limit_display_qty
        if voice_display == 'unlimited':
            calls = 'unlimited'
        else:
            calls = int(obj.voice_limit) if obj.voice_limit else 0

        sms_display = obj.sms_limit_display_qty
        if sms_display == 'unlimited':
            texts = 'unlimited'
        else:
            texts = int(obj.sms_limit) if obj.sms_limit else 0

        return {
            "data": data,
            "calls": calls,
            "texts": texts,
            "europe_data": obj.base_eu_roaming_days
        }

    def get_travel_addons(self, obj):
        plan_travel_addons = PlanTravelAddon.objects.filter(
            plan=obj,
            is_active=True,
            travel_addon__is_active=True
        ).select_related('travel_addon').prefetch_related('travel_addon__roaming_esim_packages')

        return TravelAddonSerializer(plan_travel_addons, many=True).data

    def get_family_plans(self, obj):
        family_links = FamilyPlanLink.objects.filter(
            main_plan=obj,
            is_active=True
        ).select_related('sub_plan')

        return FamilyPlanSerializer(family_links, many=True).data

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'name': instance.name,
            'allowances': self.get_allowances(instance),
            'travel_addons': self.get_travel_addons(instance),
            'price': float(instance.price),
            'family_plans': self.get_family_plans(instance)
        }
