import logging
from math import ceil
from dateutil.relativedelta import relativedelta
from django.contrib.auth.mixins import UserPassesTestMixin
from django.http import HttpResponse, HttpResponseBadRequest
from django.shortcuts import get_object_or_404, redirect, render
from django.views import View
from django.conf import settings
from django.utils import timezone
from rest_framework import status, views, viewsets
from rest_framework.response import Response
from core.models import Subscriber, Perk, PerkPointAllocation
from subscriber_app.nexus_interface import NexusInterface
from subscriber_app.views import BearerTokenAuthentication
from .models import (
    Alert, Draw, Notification, Onboarding, Prize, StaticFile
)
from .serializers import (
    AlertSerializer, CancellationSummarySerializer, DrawSerializer,
    NotificationSerializer, OnboardingDetailsSerializer, PrizeSerializer
)

class SuperuserRequiredMixin(UserPassesTestMixin):
    def test_func(self):
        return self.request.user.is_superuser

class ManualDrawDetailView(SuperuserRequiredMixin, View):
    template_name = 'draw_detail.html'

    def get(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        prizes = Prize.objects.filter(draw=draw)
        context = {
            'draw': draw,
            'prizes': prizes,
        }
        return render(request, self.template_name, context)

    def post(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        entries = int(request.POST.get('entries'))
        if draw.status == Draw.DrawStatus.RUNNING:
            draw.reset()
            draw.refresh_from_db()
        if draw.status == Draw.DrawStatus.DRAFT:
            draw.start_draw(entries)
        return redirect('draw_detail', draw_id=draw.id)


class SetPrizesView(SuperuserRequiredMixin, View):
    template_name = 'set_prizes.html'

    def get(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        if draw.status != Draw.DrawStatus.DRAFT:
            return HttpResponseBadRequest("Draw must be in draft status to set prizes.")
        return render(request, self.template_name, {'draw': draw})

    def post(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        if draw.status != Draw.DrawStatus.DRAFT:
            return HttpResponseBadRequest("Draw must be in draft status to set prizes.")

        prize_list = request.POST.get('prizes')
        if not prize_list:
            return HttpResponseBadRequest("Prizes list cannot be empty.")

        Prize.objects.filter(draw=draw).delete()

        lines = prize_list.split('\n')
        total_prizes = 0
        for line in lines:
            line = line.strip()
            if 'x' in line:
                count, name = line.split('x', 1)
                count = int(count.strip())
                name = name.strip()
                for _ in range(count):
                    Prize.objects.create(draw=draw, name=name)
                    total_prizes += 1

        while total_prizes < draw.total_pool_size:
            Prize.objects.create(draw=draw, name='Empty Prize', is_empty=True)
            total_prizes += 1

        return redirect('draw_detail', draw_id=draw.id)


class DrawListView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def get(self, request, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        draws = Draw.objects.filter(client_id=client_id).prefetch_related('prizes__prize_template').all()
        serializer = DrawSerializer(draws, many=True, context={'subscriber': subscriber})
        return Response(serializer.data)

class DrawDetailView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def get(self, request, client_id, pk):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        try:
            draw = Draw.objects.filter(client_id=client_id).prefetch_related('prizes__prize_template').get(pk=pk)
        except Draw.DoesNotExist:
            return Response({'detail': 'Draw not found.'}, status=status.HTTP_404_NOT_FOUND)

        serializer = DrawSerializer(draw, context={'subscriber': subscriber})
        return Response(serializer.data)

class SpinPrizeView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def post(self, request, client_id, pk):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        try:
            draw = Draw.objects.filter(client_id=client_id).prefetch_related('prizes').get(pk=pk)
        except Draw.DoesNotExist:
            return Response({'detail': 'Draw not found.'}, status=status.HTTP_404_NOT_FOUND)

        prize = draw.draw_a_prize(subscriber)
        if not prize:
            return Response({'detail': 'No prizes available for this draw.'}, status=status.HTTP_400_BAD_REQUEST)

        return Response(PrizeSerializer(prize).data)


class PointsLedgerView(views.APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        point_allocations = PerkPointAllocation.objects.filter(subscription__subscriber=subscriber).order_by('-date')

        perk_redemptions = subscriber.perk_redemptions.all().order_by('-redeemed_on')
        perk_data = [
            {
                'id': redemption.id,
                'title': redemption.perk.title,
                'real_date': redemption.redeemed_on,
                'image_url': redemption.perk.perk_image_link,
                'points': -redemption.points_paid,
            }
            for redemption in perk_redemptions
        ]
        points_data = [
            {
                'id': allocation.id,
                'title': 'Points',
                'real_date': allocation.date,
                'image_url': '',
                'points': allocation.amount
            }
            for allocation in point_allocations[:7]
        ]
        coins = []
        spins = []
        if settings.SIMP_DAILY_POINTS:
            upcoming_daily_coins = [
                {
                    'id': 0,
                    'title': 'Simp coins',
                    'real_date': timezone.now() + relativedelta(days=i),
                    'image_url': 'https://staging.d16lfu7yd6akrx.amplifyapp.com/rewards/REWARDS_Coins.jpg',
                    'points': settings.SIMP_DAILY_POINTS
                }
                for i in range(1, 5)
            ]
            upcoming_month_coins = [
                {
                    'id': 0,
                    'title': 'Simp coins',
                    'real_date': timezone.now() + relativedelta(months=months),
                    'image_url': 'https://staging.d16lfu7yd6akrx.amplifyapp.com/rewards/REWARDS_Coins.jpg',
                    'points': int(((timezone.now() + relativedelta(months=months)) - timezone.now()).days) * settings.SIMP_DAILY_POINTS
                }
                for months in [1,2,3,6,9]
            ]
            coins = upcoming_daily_coins + upcoming_month_coins
            spins = [
                {
                    'id': 0,
                    'title': 'Simp spins',
                    'real_date': timezone.now() + relativedelta(days=i),
                    'image_url': 'https://staging.d16lfu7yd6akrx.amplifyapp.com/rewards/Spins_HeroImage_GENERIC.jpg',
                    'points': 0
                }
                for i in range(1, 5)
            ]
        sorted_by_date = sorted(points_data + perk_data + spins + coins, key=lambda x: x['real_date'], reverse=True)
        data = [
            {
                'id': item['id'],
                'title': item['title'],
                'date': item['real_date'].strftime('%Y-%m-%d'),
                'image_url': item['image_url'],
                'points': item['points']
            }
            for item in sorted_by_date
        ]
        return Response(data)


def serve_static_file(request, filename):
    static_file = get_object_or_404(StaticFile, name=filename)
    response = HttpResponse(static_file.data, content_type=static_file.content_type)
    response["Content-Disposition"] = f'inline; filename="{static_file.name}"'
    return response


def get_subscriber_by_cognito_username(cognito_username, client_id):
    return Subscriber.objects.filter(cognito_username=cognito_username, client_id=client_id).get()


class OnboardingDetailsView(views.APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        if not Onboarding.objects.filter(subscriber=subscriber).exists():
            print(f"Creating onboarding for {subscriber}")
            Onboarding.objects.create(subscriber=subscriber, onboarding_details={})

        onboarding_details = subscriber.onboarding_details

        serializer = OnboardingDetailsSerializer(onboarding_details.onboarding_details)
        print(serializer.data, subscriber, request.cognito_username)
        return Response(serializer.data)

    def put(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        onboarding = subscriber.onboarding_details

        onboarding.onboarding_details.update(request.data)
        onboarding.save()
        serializer = OnboardingDetailsSerializer(onboarding.onboarding_details)
        return Response(serializer.data)


class EsimQRView(views.APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        sim = subscriber.subscriptions.primary().first().latest_sim
        # serve qr as an image
        return HttpResponse(sim.esim_qr_as_png, content_type='image/png')


class AlertViewSet(viewsets.ModelViewSet):
    serializer_class = AlertSerializer
    permission_classes = [BearerTokenAuthentication]

    def get_queryset(self):
        subscriber = get_subscriber_by_cognito_username(self.request.cognito_username, self.kwargs.get('client_id'))
        return Alert.objects.for_subscriber(subscriber)

    def destroy(self, request, *args, **kwargs):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, self.kwargs.get('client_id'))
        alert = self.get_object()
        alert.mark_as_dismissed(subscriber)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def create(self, request, *args, **kwargs):
        return Response({"detail": "Method Not Allowed"}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response({"detail": "Method Not Allowed"}, status=status.HTTP_405_METHOD_NOT_ALLOWED)


class SimpNotificationsView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        notifications = Notification.objects.filter(subscriber=subscriber)
        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data)

    def delete(self, request, notification_id, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        try:
            notification = Notification.objects.get(id=notification_id, subscriber=subscriber)
            notification.mark_as_seen()
            return Response(status=status.HTTP_204_NO_CONTENT, headers={'Content-Length': '0'})
        except Notification.DoesNotExist:
            return Response(
                {"detail": "Notification not found"},
                status=status.HTTP_404_NOT_FOUND
            )

logger = logging.getLogger(__name__)

class CancellationSummaryView(views.APIView):
    permission_classes = [BearerTokenAuthentication]

    def _get_days_for_next_month(self, months_needed, subscriber):
        if months_needed == 1:
            time_control = subscriber.client.provider.time_control()
            return time_control.days_left_this_month
        return months_needed * 31

    def _get_subscription_end_date(self, subscriber):
        try:
            nexus = NexusInterface(subscriber.client.id)
            end_date = nexus.get_subscription_end_date(subscriber.cognito_username)
            if end_date:
                return int(end_date)
        except (ValueError, AttributeError) as e:
            logger.error('Failed to get subscription end date: %s', str(e))
        return None

    def _calculate_tenure_days(self, perk, time_control):
        completed = time_control.get_complete_months()
        days = time_control.days_left_this_month
        months_remaining = int(perk.eligibility_threshold) - int(completed) - 1
        if months_remaining < 0:
            return 0
        return (months_remaining * 31) + int(days)

    def _calculate_rate_based_days(self, subscriber, perk, subscription):
        if perk.eligibility_type == Perk.EligibilityType.TOTAL_SPEND:
            rate = subscription.next_bill_amount()
            remaining = perk.eligibility_threshold - subscriber.total_spend
        elif perk.eligibility_type == Perk.EligibilityType.TOTAL_POINTS_EARNED:
            if not subscription.latest_plan:
                return 0
            rate = subscription.latest_plan.points_per_month
            remaining = perk.eligibility_threshold - subscriber.total_points_earned
        else:
            if not (perk.elective_redemption_cost and subscription.latest_plan):
                return 0
            rate = subscription.latest_plan.points_per_month
            remaining = perk.elective_redemption_cost - subscriber.perk_points
            if remaining <= 0:
                return 0
        if rate <= 0:
            return 0
        months_needed = ceil(remaining / rate)
        return self._get_days_for_next_month(months_needed, subscriber)

    def _calculate_days_left(self, subscriber, perk):
        days_left = 0
        if subscriber.progress_towards_perk(perk) >= 75:
            return 0
        if perk.eligibility_type == Perk.EligibilityType.AIRDROP:
            return 0
        if not (subscription := subscriber.subscriptions.first()):
            return 0
        if perk.eligibility_type == Perk.EligibilityType.TENURE:
            time_control = subscriber.client.provider.time_control()
            days_left = self._calculate_tenure_days(perk, time_control)
        elif perk.eligibility_type in (
            Perk.EligibilityType.TOTAL_SPEND,
            Perk.EligibilityType.TOTAL_POINTS_EARNED,
            Perk.EligibilityType.NO_FREE
        ):
            days_left = self._calculate_rate_based_days(subscriber, perk, subscription)
        return days_left

    def _get_upcoming_rewards(self, subscriber):
        claimed_perk_ids = set(subscriber.perk_redemptions.values_list('perk_id', flat=True))
        perks = subscriber.client.perks.filter(
            enabled=True
        ).exclude(
            id__in=claimed_perk_ids
        ).select_related('polymorphic_ctype')
        upcoming_rewards = []
        for perk in perks:
            try:
                progress = subscriber.progress_towards_perk(perk)
                if progress > 50:
                    days_left = self._calculate_days_left(subscriber, perk)
                    upcoming_rewards.append({
                        'id': perk.id,
                        'title': perk.title,
                        'progress': progress,
                        'days_left': days_left,
                        'image_link': perk.image_link,
                        'badge': {
                            'type': 'perk',
                            'text': ''
                        }
                    })
            except (ValueError, AttributeError) as e:
                logger.error('Failed to process perk %s: %s', perk.id, str(e))
        if not settings.TESTING_MODE:
            upcoming_rewards.append({
                'id': 1,
                'title': 'Weekly Points',
                'progress': 75,
                'days_left': 3,
                'image_link': 'https://example.com/image.png',
                'badge': {
                    'type': 'coins',
                    'text': '+200',
                }
            })
            upcoming_rewards.append({
                'id': 2,
                'title': 'Spins',
                'progress': 0,
                'days_left': 0,
                'image_link': 'https://example.com/image.png',
                'badge': {
                    'type': 'ticket',
                    'text': '1 ticket',
                }
            })
            upcoming_rewards.append({
                'id': 3,
                'title': 'Spins',
                'progress': 25,
                'days_left': 1,
                'image_link': 'https://example.com/image.png',
                'badge': {
                    'type': 'ticket',
                    'text': '12 tickets',
                }
            })
            upcoming_rewards.append({
                'id': 4,
                'title': 'Spins',
                'progress': 5,
                'days_left': 10,
                'image_link': 'https://example.com/image.png',
                'badge': {
                    'type': 'ticket',
                    'text': '12 tickets',
                }
            })
            upcoming_rewards.append({
                'id': 5,
                'title': 'Weekly Points',
                'progress': 95,
                'days_left': 12,
                'image_link': 'https://example.com/image.png',
                'badge': {
                    'type': 'coins',
                    'text': '+100',
                }
            })
        return upcoming_rewards

    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        subscription = subscriber.subscriptions.first()
        cancellation_date = self._get_subscription_end_date(subscriber) if subscription else None

        data = {
            'referral_credit': str(subscriber.referral_credit),
            'perk_points': subscriber.perk_points,
            'cancellation_date': int(cancellation_date) if cancellation_date else None,
            'upcoming_rewards': self._get_upcoming_rewards(subscriber)
        }
        serializer = CancellationSummarySerializer(data=data)
        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            logger.error('Failed to validate cancellation summary data: %s', str(e))
            raise
        return Response(serializer.validated_data)
