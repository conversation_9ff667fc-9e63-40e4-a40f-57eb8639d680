# Generated by Django 4.2.7 on 2025-02-26 01:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0160_client_openid_scopes'),
        ('simp', '0005_merge_0004_onboarding_0004_staticfile'),
    ]

    operations = [
        migrations.AddField(
            model_name='draw',
            name='image_link',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='onboarding',
            name='onboarding_details',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name='onboarding',
            name='subscriber',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_details', to='core.subscriber'),
        ),
    ]
