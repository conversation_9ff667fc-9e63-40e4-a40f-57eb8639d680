# Generated by Django 4.2.7 on 2025-02-10 16:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('simp', '0003_prizetemplate_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaticFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('content_type', models.CharField(help_text='MIME type of the file', max_length=255)),
                ('data', models.BinaryField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
