import logging
import os
import requests

logger = logging.getLogger()
logger.setLevel(logging.INFO)


def lambda_handler(event, __):
    slack_endpoint = os.environ['SLACK_ENDPOINT']

    headers = {
        'Content-Type': 'application/json',
    }
    try:
        response = requests.post(slack_endpoint, json={**event, 'text': f'v3 - {event["text"]}'},headers=headers, timeout=60)
        response.raise_for_status()
    except Exception as e:  # pylint: disable=broad-exception-caught
        if event and event.get('ignoreFailures', False):
            logger.info('Ignoring failure: %s', e)
        else:
            raise e
