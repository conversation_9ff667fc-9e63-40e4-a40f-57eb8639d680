# pylint: disable=duplicate-code
# pylint: disable=wrong-import-position
import logging
import django

django.setup()

from core.plan_change import PlanChangeManager

logger = logging.getLogger()
logger.setLevel(logging.INFO)

class CheckNotReadyError(Exception):
    pass

def validate(manager):
    manager.validate_plan_change()

def validate_and_lock(manager):
    validate(manager)
    manager.lock_plan_change_for_actuation()

def bill(manager):
    manager.adjust_billing_for_plan_change()

def check_billing_change(manager):
    if not manager.is_billing_change_complete():
        raise CheckNotReadyError()

def effect_provider_change(manager):
    manager.make_appropriate_changes_in_provider()

def check_provider_change(manager):
    if not manager.is_provider_change_complete():
        raise CheckNotReadyError()

def complete(manager):
    manager.complete_plan_change()

def lambda_handler(event, __):
    manager = PlanChangeManager.for_sub_execution(event['plan_change_id'], event['sub_execution_id'])
    action = event['action']
    {
        'validate': validate,
        'validate_and_lock': validate_and_lock,
        'bill': bill,
        'check_billing_change': check_billing_change,
        'effect_provider_change': effect_provider_change,
        'check_provider_change': check_provider_change,
        'complete': complete
    }[action](manager)
    return manager.serialise_plan_change()
