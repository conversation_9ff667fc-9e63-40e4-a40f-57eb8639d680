"""
Django settings for nexus project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import sys
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-412g6!ux-h8o6ajtdiaeha3*txq*sx8k*i8^83j_2=k2gtevaw'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'polymorphic',
    'core',
    'simp',
    'next_api',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'nexus.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'nexus.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "nexus",
        "HOST": os.environ.get('DB_HOST', 'localhost'),
        "USER": os.environ.get('DB_USER', 'cdr_db'),
        "PASSWORD": os.environ.get('DB_PASSWORD'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-gb'

TIME_ZONE = 'Europe/London'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = 'static'
# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

CORS_ALLOW_ALL_ORIGINS = True

CDR_DB_API_ENDPOINT = 'https://cdr-db.shaka.tel/api/v1'
CDR_DB_API_TOKEN = os.environ.get('CDR_DB_API_TOKEN')

LAMBDA_API_TOKEN = os.environ.get('LAMBDA_API_TOKEN')

TRANSATEL_WEBHOOK_SECRET_KEY = os.environ.get('TRANSATEL_WEBHOOK_SECRET_KEY')
BETA_TRANSATEL_WEBHOOK_SECRET_KEY = os.environ.get('BETA_TRANSATEL_WEBHOOK_SECRET_KEY')
YAYZI_TRANSATEL_WEBHOOK_SECRET_KEY = os.environ.get('YAYZI_TRANSATEL_WEBHOOK_SECRET_KEY')

BETA_REFDATA = {
    'CLIENT_ID': os.environ.get('BETA_CLIENT_ID'),
}

YAYZI_REFDATA = {
    'CLIENT_ID': os.environ.get('YAYZI_CLIENT_ID'),
}

SIX_REFDATA = {
    'CLIENT_ID': os.environ.get('SIX_CLIENT_ID'),
    '100GB_PLAN_ID': os.environ.get('SIX_100GB_PLAN_ID'),
    '30GB_PLAN_ID': os.environ.get('SIX_30GB_PLAN_ID')
}

STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY')
STRIPE_WEBHOOK_SECRET_KEY = os.environ.get('STRIPE_WEBHOOK_SECRET_KEY')

IGNORABLE_CLIENTS = []

HMAC_KEY = os.environ.get('HMAC_KEY', SECRET_KEY)
ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY', HMAC_KEY[:32])
INOVO_BETA_HACK = os.environ.get('INOVO_BETA_HACK', False)
SEND_SIMP_NOTIFICATIONS = os.environ.get('SEND_SIMP_NOTIFICATIONS', False)
AUTO_CREATE_ONBOARDING_DETAILS = os.environ.get('AUTO_CREATE_ONBOARDING_DETAILS', False)
SIMP_DAILY_POINTS = os.environ.get('SIMP_DAILY_POINTS', 0)

DEFAULT_SPINS_PER_DRAW = 1

PLAN_UPGRADE_STATE_MACHINE_ARN = os.environ.get('PLAN_UPGRADE_STATE_MACHINE_ARN')
PLAN_DOWNGRADE_STATE_MACHINE_ARN = os.environ.get('PLAN_DOWNGRADE_STATE_MACHINE_ARN')
CANCEL_PLAN_CHANGE_STATE_MACHINE_ARN = os.environ.get('CANCEL_PLAN_CHANGE_STATE_MACHINE_ARN')
CANCEL_SUBSCRIPTION_STATE_MACHINE_ARN = os.environ.get('CANCEL_SUBSCRIPTION_STATE_MACHINE_ARN')

STRIPE_TEST_PUBLIC_KEY = os.environ.get('STRIPE_TEST_PUBLIC_KEY')
STRIPE_TEST_SECRET_KEY = os.environ.get('STRIPE_TEST_SECRET_KEY')
STRIPE_TEST_WEBHOOK_SECRET_KEY = os.environ.get('STRIPE_TEST_WEBHOOK_SECRET_KEY')

BILLING_CYCLE_FREEZE_HOURS = os.environ.get('BILLING_CYCLE_FREEZE_HOURS', 5)
BILLING_CYCLE_EXPECT_PAYMENT_AFTER_HOURS = os.environ.get('BILLING_CYCLE_EXPECT_PAYMENT_AFTER_HOURS', 24)
GAMMA_WEBHOOK_SECRET_KEY = os.environ.get('GAMMA_WEBHOOK_SECRET_KEY')

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.db.DatabaseCache",
        "LOCATION": "front_cache",
        "TIMEOUT": 1200,
        "OPTIONS": {
            "MAX_ENTRIES": 1000,
        }
    }
}

SITE_URL = 'http://localhost:8000'
RUN_DEV_THREAD_RECACHE = True
ENABLE_FRONT_CACHE = True

GOOGLE_AUTH_CLIENT_ID = os.environ.get('GOOGLE_AUTH_CLIENT_ID')
GOOGLE_AUTH_POOL_ID = os.environ.get('GOOGLE_AUTH_POOL_ID')

APPLE_AUTH_CLIENT_ID = os.environ.get('APPLE_AUTH_CLIENT_ID')
APPLE_AUTH_POOL_ID = os.environ.get('APPLE_AUTH_POOL_ID')


TELNA_API_KEY = os.environ.get('TELNA_API_KEY')

STRIPE_BETA = os.environ.get('STRIPE_BETA', False)

SLACK_CLIENT_PK_CUTOFF = os.environ.get('SLACK_CLIENT_PK_CUTOFF', 0)

FORCE_ESIM = os.environ.get('FORCE_ESIM', False)

REQUIRE_PERK_REDEMPTION_CONFIRMATION = ['simpnexus']

TESTING_MODE = str(os.environ.get('TESTING_MODE', len(sys.argv) > 1 and sys.argv[1] == 'test')).lower() == 'true'
# Build trigger
FORWARD_NORDIC_TO_SIMP = os.environ.get('FORWARD_NORDIC_TO_SIMP', False)

try:
    from .local_settings import *  # pylint: disable=wildcard-import, unused-wildcard-import
except ImportError:
    pass
