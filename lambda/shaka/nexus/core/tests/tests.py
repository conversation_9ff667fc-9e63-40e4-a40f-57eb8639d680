import json
from django.test import TestCase, RequestFactory, override_settings
from django.core.management import call_command
from rest_framework import status
from rest_framework.test import APIClient
import dateutil.parser
from core.webhooks import InfobipWebhookAction
from core.models import (
    Client, SMSMessage, IndividualSMSDelivery, PerkPointAllocation,
    Subscription, PlanDiscount
)
from core import test_utils
from core.views import get_username_from_jwt
from core.utils import phone_number_to_msisdn
from core.billing_interface import BillingClient
from core.models import Referral
from .factories import (
    SubscriptionFactory, DEFAULT_DATA_LIMIT_GB, SubscriptionWithPortInFactory,
    DashboardUserFactory, ClientFactory, SMSMessageFactory, DiscountPerkFactory,
    VoucherPerkFactory, BoltOnPerkFactory, ClientBoltOnFactory, SubscriberFactory,
    PlanFactory, PlanDiscountFactory
)

class ClientApiTest(TestCase):
    def setUp(self):
        self.client = test_utils.create_client('Test Client')

    def test_client_api_read_only(self):
        client = APIClient()

        response = client.post('/api/clients/', {'name': 'New Client'})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        self.assertEqual(Client.objects.count(), 1)

        response = client.put(f'/api/clients/{self.client.obfuscated_id}/', {'name': 'Updated Client'})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        self.client.refresh_from_db()
        self.assertEqual(self.client.name, 'Test Client')


class DiscountPerkAPITest(test_utils.DashboardAPITestCase):
    def test_discount_perk_create(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/perks/', {'name': 'Test discount perk', 'description': 'Test description', 'eligibility_type': 'tenure', 'eligibility_threshold': 1, 'discount_details': {'plan_discount': {'discount_type': 'percentage', 'discount_amount': 10, 'discount_duration_months': 1}}}, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert self.client.perks.count() == 1
            assert self.client.perks.get().name == 'Test discount perk'
            assert self.client.perks.get().plan_discount.discount_type == 'percentage'
            assert self.client.perks.get().plan_discount.discount_amount == 10

    def test_discount_perk_create_does_not_allow_setting_client(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            other_client = ClientFactory()
            response = client.post('/api/perks/', {'name': 'Test discount perk', 'eligibility_type': 'tenure', 'client': other_client.id, 'client_id': other_client.id, 'discount_details': {'plan_discount': {'discount_type': 'percentage', 'discount_amount': 10, 'discount_duration_months': 1}}}, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert self.client.perks.count() == 1
            assert self.client.perks.get().client == self.client

    def test_discount_perks_appear_in_response(self):
        perk = DiscountPerkFactory(client=self.client)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.get('/api/perks/')
            assert response.status_code == status.HTTP_200_OK
            assert len(response.data) == 1
            assert response.data[0]['id'] == perk.id
            assert response.data[0]['discount_details']['plan_discount']['discount_type'] == perk.plan_discount.discount_type


class VoucherPerkAPITest(test_utils.DashboardAPITestCase):
    def test_voucher_perk_create(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/perks/', {'name': 'Test voucher perk', 'description': 'Test description', 'eligibility_type': 'tenure', 'eligibility_threshold': 1, 'voucher_details': {'merchant_name': 'Z', 'code': 'abc'}}, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert self.client.perks.count() == 1
            assert self.client.perks.get().name == 'Test voucher perk'
            assert self.client.perks.get().merchant_name == 'Z'
            assert self.client.perks.get().code == 'abc'

    def test_voucher_perk_create_does_not_allow_setting_client(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            other_client = ClientFactory()
            response = client.post('/api/perks/', {'name': 'Test voucher perk', 'eligibility_type': 'tenure', 'client': other_client.id, 'client_id': other_client.id, 'voucher_details': {'merchant_name': 'Z', 'code': 'abc'}}, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert self.client.perks.count() == 1
            assert self.client.perks.get().client == self.client

    def test_voucher_perks_appear_in_response(self):
        perk = VoucherPerkFactory(client=self.client)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.get('/api/perks/')
            assert response.status_code == status.HTTP_200_OK
            assert len(response.data) == 1
            assert response.data[0]['id'] == perk.id
            assert response.data[0]['voucher_details']['code'] == perk.code


class BoltOnPerkAPITest(test_utils.DashboardAPITestCase):
    def setUp(self):
        super().setUp()
        self.bolt_on = ClientBoltOnFactory(client=self.client)

    def test_bolt_on_perk_create(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/perks/', {'name': 'Test bolt_on perk', 'description': 'Test description', 'eligibility_type': 'tenure', 'eligibility_threshold': 1, 'bolt_on_details': {'bolt_on': self.bolt_on.pk}}, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert self.client.perks.count() == 1
            assert self.client.perks.get().name == 'Test bolt_on perk'
            assert str(self.client.perks.get().bolt_on.pk) == str(self.bolt_on.pk)

    def test_bolt_on_perk_create_does_not_allow_setting_client(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            other_client = ClientFactory()
            response = client.post('/api/perks/', {'name': 'Test bolt_on perk', 'eligibility_type': 'tenure', 'client': other_client.id, 'client_id': other_client.id, 'bolt_on_details': {'bolt_on': self.bolt_on.pk}}, format='json')
            assert response.status_code == status.HTTP_201_CREATED
            assert self.client.perks.count() == 1
            assert self.client.perks.get().client == self.client

    def test_bolt_on_perks_appear_in_response(self):
        perk = BoltOnPerkFactory(client=self.client, bolt_on=self.bolt_on)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.get('/api/perks/')
            assert response.status_code == status.HTTP_200_OK
            assert len(response.data) == 1
            assert response.data[0]['id'] == perk.id
            assert response.data[0]['bolt_on_details']['bolt_on'] == self.bolt_on.pk


class SMSApiTest(test_utils.DashboardAPITestCase):
    def setUp(self):
        super().setUp()
        self.subscription = SubscriptionFactory(using=True, subscriber__client=self.client, subscriber__send_marketing=True)

    def test_sms_create(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message', 'send_on': '2020-01-01T00:00:00Z'})
            assert response.status_code == status.HTTP_201_CREATED
            msg = SMSMessage.objects.get()
            assert msg.title == 'Test title'
            assert msg.message == 'Test message'
            assert msg.send_on == dateutil.parser.isoparse('2020-01-01T00:00:00Z')
            assert msg.status == SMSMessage.Status.SENT
            assert msg.client == self.dashboard_user.client
            assert msg.initiator == self.dashboard_user

    def test_sms_create_does_not_allow_setting_initiator(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            other_initator = DashboardUserFactory(client=self.dashboard_user.client)
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message', 'send_on': '2020-01-01T00:00:00Z', 'initiator': other_initator.id})
            assert response.status_code == status.HTTP_201_CREATED
            assert SMSMessage.objects.get().initiator == self.dashboard_user
            assert SMSMessage.objects.get().initiator != other_initator

    def test_sms_create_does_not_allow_setting_client(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            other_client = ClientFactory()
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message', 'send_on': '2020-01-01T00:00:00Z', 'client': other_client.id, 'client_id': other_client.id})
            assert response.status_code == status.HTTP_201_CREATED
            assert SMSMessage.objects.get().client == self.dashboard_user.client
            assert SMSMessage.objects.get().client != other_client

    def test_sms_create_does_not_allow_setting_status(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message', 'send_on': '2020-01-01T00:00:00Z', 'status': SMSMessage.Status.SENT})
            assert response.status_code == status.HTTP_201_CREATED
            assert SMSMessage.objects.get().status == SMSMessage.Status.SENT

    def test_schedule_sms_create(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message', 'send_on': test_utils.future_date_str()})
            assert response.status_code == status.HTTP_201_CREATED
            assert SMSMessage.objects.get().status == SMSMessage.Status.TO_SEND

    def test_sms_update_works(self):
        sms = SMSMessageFactory(to_send=True, client=self.dashboard_user.client, initiator=self.dashboard_user)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.put(f'/api/frontend/sms/{sms.id}/', {'title': 'Test title updated','message': 'Updated', 'send_on': test_utils.future_date_str()})
            assert response.status_code == status.HTTP_200_OK
            assert SMSMessage.objects.get().message == 'Updated'
            assert SMSMessage.objects.get().title == 'Test title updated'

    def test_sms_update_fails_for_sending_message(self):
        sms = SMSMessageFactory(sending=True, client=self.dashboard_user.client, initiator=self.dashboard_user)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.put(f'/api/frontend/sms/{sms.id}/', {'message': 'Updated'})
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert SMSMessage.objects.get().message != 'Updated'

    def test_sms_update_fails_for_sent_message(self):
        sms = SMSMessageFactory(sent=True, client=self.dashboard_user.client, initiator=self.dashboard_user)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.put(f'/api/frontend/sms/{sms.id}/', {'message': 'Updated'})
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert SMSMessage.objects.get().message != 'Updated'

    def test_sms_delete_works(self):
        sms = SMSMessageFactory(to_send=True, client=self.dashboard_user.client, initiator=self.dashboard_user)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.delete(f'/api/frontend/sms/{sms.id}/')
            assert response.status_code == status.HTTP_204_NO_CONTENT
            assert SMSMessage.objects.count() == 0

    def test_sms_delete_fails_for_sending_message(self):
        sms = SMSMessageFactory(sending=True, client=self.dashboard_user.client, initiator=self.dashboard_user)
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.delete(f'/api/frontend/sms/{sms.id}/')
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert SMSMessage.objects.count() == 1

    def test_list_sms_only_shows_own_messages(self):
        sms = SMSMessageFactory(to_send=True, client=self.dashboard_user.client, initiator=self.dashboard_user)
        SMSMessageFactory(to_send=True, client=ClientFactory())
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.get('/api/frontend/sms/')
            assert response.status_code == status.HTTP_200_OK
            assert len(response.data) == 1
            assert response.data[0]['id'] == sms.id

    def test_sms_create_creates_delivery_reports(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message'})
            assert response.status_code == status.HTTP_201_CREATED
            msg = SMSMessage.objects.get()
            assert msg.individual_deliveries.count() == 1
            assert msg.individual_deliveries.get().recipient == self.subscription.uk_prefixed_phone_number

    def send_delivery_webhook(self, data):
        request = RequestFactory().post('/', json.dumps(data), content_type='application/json')
        InfobipWebhookAction(self.client.id).process_webhook(request)

    def test_sms_webhook_receives_delivery_reports(self):
        client = APIClient()
        with self.forced_auth_dashboard_user():
            response = client.post('/api/frontend/sms/', {'title': 'Test title', 'message': 'Test message'})
            assert response.status_code == status.HTTP_201_CREATED
            msg = SMSMessage.objects.get()
            self.send_delivery_webhook({"results": [{"to": phone_number_to_msisdn(self.subscription.latest_msisdn), "error": {"id": 0, "name": "NO_ERROR", "groupId": 0, "groupName": "OK", "permanent": False, "description": "No Error"}, "price": {"currency": "EUR", "pricePerMessage": 0.0}, "bulkId": "123", "doneAt": "2024-06-06T10:54:36.280+0000", "sender": "Test", "sentAt": "2024-06-06T10:19:34.745+0000", "status": {"id": 5, "name": "DELIVERED_TO_HANDSET", "action": None, "groupId": 3, "groupName": "DELIVERED", "description": "Message delivered to handset"}, "platform": {"entityId": None, "applicationId": None}, "messageId": "123", "callbackData": msg.pk, "messageCount": 1}]})
            delivery = msg.individual_deliveries.get()
            assert delivery.status == IndividualSMSDelivery.Status.DELIVERED


class ClientModelTest(TestCase):
    def test_auth_url(self):
        client = test_utils.create_client('Test Client', auth_app_client_id='3qrcg75skommipbs9h22ch9bv0', auth_app_domain_prefix='shakadev', auth_app_redirect_uri='http://localhost:3000')
        self.assertEqual('https://shakadev.auth.eu-west-2.amazoncognito.com/authorize?client_id=3qrcg75skommipbs9h22ch9bv0&response_type=token&scope=email+openid+phone&redirect_uri=http%3A%2F%2Flocalhost%3A3000', client.authorisation_url)


class JWTTests(TestCase):
    def test_extract_username(self):
        # Valid token but from a dev pool, no important keys leaked
        jwt = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        expected_username = '2e5f2fb7-e61e-425e-826a-6aa14eee21ec'
        self.assertEqual(get_username_from_jwt(jwt), expected_username)


class SubscriptionTests(test_utils.NexusTestCase):
    def test_subscription_payment_prorates_perk_points(self):
        subscription = SubscriptionFactory(old=True, using=True, billing_subscription_id='abc123', start_date=test_utils.halfway_through_billing_cycle())
        plan = subscription.latest_plan
        plan.points_per_month = 100
        plan.save()
        billing_interface = BillingClient(subscription.client)
        date_epoch = test_utils.halfway_through_billing_cycle().timestamp()
        billing_interface.mark_subscription_payment(billing_subscription_id='abc123', billing_invoice_id='abc1234', amount=10, currency='gbp', date_epoch=date_epoch)
        allocation = PerkPointAllocation.objects.get()
        assert allocation
        assert allocation.amount < 75
        assert allocation.amount > 25
        subscription.refresh_from_db()
        assert allocation.amount == subscription.subscriber.perk_points


class BarDataTests(test_utils.NexusTestCase):  # pylint: disable=too-many-public-methods
    usage_close_to_limit = DEFAULT_DATA_LIMIT_GB - 1
    usage_close_to_background_limit = 29.5
    usage_close_to_half_limit = (DEFAULT_DATA_LIMIT_GB/2) - 1
    quarter_limit_usage = usage_close_to_half_limit / 2
    eighth_limit_usage = quarter_limit_usage / 2
    usage_under_limit = DEFAULT_DATA_LIMIT_GB/4
    mid_tier_usage_limit = 30

    def run_bar_data_check(self):
        call_command('bar_data_for_dangerous_users')

    def _run_test(self, subscription, usage):
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=usage)
        assert not self.sim_is_barred(subscription.latest_sim)
        self.run_bar_data_check()

    def test_data_bars_based_on_usage(self):
        subscription = SubscriptionFactory(old=True, using=True)
        self._run_test(subscription, self.usage_close_to_limit)
        assert self.fully_barred_sim(subscription)

    def test_data_bars_gamma(self):
        subscription = SubscriptionFactory(old=True, using=True, subscriber__client__provider__name='gamma')
        self._run_test(subscription, self.usage_close_to_limit)
        assert self.fully_barred_sim(subscription)

    def test_does_not_bar_if_under_usage(self):
        subscription = SubscriptionFactory(old=True, using=True)
        self._run_test(subscription, self.usage_under_limit)
        assert self.did_not_bar_sim(subscription)

    def test_data_bars_based_on_prorated_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self._run_test(subscription, self.usage_close_to_half_limit)
        assert self.fully_barred_sim(subscription)

    def test_does_not_bar_if_under_prorated_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self._run_test(subscription, self.usage_under_limit)
        assert self.did_not_bar_sim(subscription)

    def test_bar_takes_into_account_other_sizes(self):
        subscription = SubscriptionFactory(using_mid_tier=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.mid_tier_usage_limit)
        self._run_test(subscription, self.mid_tier_usage_limit)
        assert self.fully_barred_sim(subscription)

    def test_bar_takes_into_account_plan_change_mid_cycle(self):
        subscription = SubscriptionFactory(using_mid_tier=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.start_new_plan(subscription, test_utils.three_quarters_through_billing_cycle(), DEFAULT_DATA_LIMIT_GB)
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.mid_tier_usage_limit + 1)
        self.run_bar_data_check()
        assert self.did_not_bar_sim(subscription)

    def test_data_bars_multiple_sims_and_plans_and_clients(self):
        subscription_a = SubscriptionFactory(old=True, using=True)
        subscription_b = SubscriptionFactory(old=True, using=True)
        assert subscription_a.client != subscription_b.client
        assert subscription_a.latest_plan != subscription_b.latest_plan
        assert subscription_a.latest_sim.serial_number != subscription_b.latest_sim.serial_number
        self.scenario.set_data_usage_this_billing_cycle(subscription_a, usage_gb=self.usage_close_to_limit)
        self.scenario.set_data_usage_this_billing_cycle(subscription_b, usage_gb=self.usage_close_to_limit)
        self.run_bar_data_check()
        assert self.fully_barred_sim(subscription_a)
        assert self.fully_barred_sim(subscription_b)

    def test_data_bars_when_usage_spread_across_numbers_due_to_late_port(self):
        subscription = SubscriptionWithPortInFactory(old=True, using=True, port_in_date=test_utils.halfway_through_billing_cycle())
        assert subscription.latest_sim.subscription_assignments.count() == 1
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_half_limit, number=1)
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_half_limit, number=2)
        self.run_bar_data_check()
        assert self.fully_barred_sim(subscription)

    def test_warns_and_does_not_bar_if_very_low_bar_limit(self):
        subscription = SubscriptionFactory(old=True, using=True)
        plan = subscription.latest_plan
        plan.data_bar_or_upgrade_threshold_override_gb = 0.1
        plan.save()
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_limit)
        self.run_bar_data_check()
        assert not self.sim_is_barred(subscription.latest_sim)
        assert self.fake_services.slack_was_sent_containing('strange bar or upgrade threshold')

    def test_warns_and_does_not_bar_if_very_low_warning_limit(self):
        subscription = SubscriptionFactory(old=True, using=True)
        plan = subscription.latest_plan
        plan.data_warning_threshold_override_gb = 0.1
        plan.save()
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_limit)
        self.run_bar_data_check()
        assert not self.sim_is_barred(subscription.latest_sim)
        assert self.fake_services.slack_was_sent_containing('strange warning threshold')

    def fully_barred_sim(self, subscription, sim=1):
        return self.fake_services.sim_is_barred(subscription.sim_assignments.order_by('-start_date', '-id')[sim-1].sim) and self.fake_services.slack_was_sent(f'Barring {subscription.latest_msisdn} on {subscription.latest_plan.name}')

    def did_not_bar_sim(self, subscription):
        return (not self.fake_services.sim_is_barred(subscription.latest_sim)) and self.fake_services.no_slack_was_sent()

    def test_sim_swap_retains_first_sim_usage_and_proration_and_will_not_bar_with_split_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())  # real limit is 1/2
        self.scenario.sim_swap(subscription, on_date=test_utils.three_quarters_through_billing_cycle())  # False proration would be 1/4
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.eighth_limit_usage, sim=1)
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.eighth_limit_usage, sim=2)
        self.run_bar_data_check()
        assert not self.fully_barred_sim(subscription, sim=1)
        assert not self.fully_barred_sim(subscription, sim=2)

    def test_sim_swap_retains_first_sim_usage_and_proration_and_will_not_bar_with_latest_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.sim_swap(subscription, on_date=test_utils.three_quarters_through_billing_cycle())
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.quarter_limit_usage, sim=1)
        self.run_bar_data_check()
        assert not self.fully_barred_sim(subscription, sim=1)
        assert not self.fully_barred_sim(subscription, sim=2)

    def test_sim_swap_retains_first_sim_usage_and_proration_and_will_not_bar_with_oldest_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.sim_swap(subscription, on_date=test_utils.three_quarters_through_billing_cycle())
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.quarter_limit_usage, sim=2)
        self.run_bar_data_check()
        assert not self.fully_barred_sim(subscription, sim=1)
        assert not self.fully_barred_sim(subscription, sim=2)

    def test_sim_swap_retains_first_sim_usage_and_proration_and_will_bar_with_split_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.sim_swap(subscription, on_date=test_utils.three_quarters_through_billing_cycle())
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.quarter_limit_usage, sim=1)
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.quarter_limit_usage, sim=2)
        self.run_bar_data_check()
        assert self.fully_barred_sim(subscription, sim=1)
        assert not self.fully_barred_sim(subscription, sim=2)

    def test_sim_swap_retains_first_sim_usage_and_proration_and_will_bar_with_latest_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.sim_swap(subscription, on_date=test_utils.three_quarters_through_billing_cycle())
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_half_limit, sim=1)
        self.run_bar_data_check()
        assert self.fully_barred_sim(subscription, sim=1)
        assert not self.fully_barred_sim(subscription, sim=2)

    def test_sim_swap_retains_first_sim_usage_and_proration_and_will_bar_with_oldest_usage(self):
        subscription = SubscriptionFactory(using=True, start_date=test_utils.halfway_through_billing_cycle())
        self.scenario.sim_swap(subscription, on_date=test_utils.three_quarters_through_billing_cycle())
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_half_limit, sim=2)
        self.run_bar_data_check()
        assert self.fully_barred_sim(subscription, sim=1)
        assert not self.fully_barred_sim(subscription, sim=2)

    def test_background_plan_change_causes_upgrade(self):
        subscription = SubscriptionFactory(old=True, using=True)
        self.scenario.lower_background_plan(subscription.latest_sim)
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_background_limit)
        self.run_bar_data_check()
        assert self.sim_plan_upgraded(subscription)

    def test_background_plan_change_warns(self):
        subscription = SubscriptionFactory(old=True, using=True)
        self.scenario.lower_background_plan(subscription.latest_sim)
        self.scenario.set_data_usage_this_billing_cycle(subscription, usage_gb=self.usage_close_to_background_limit-1)
        self.run_bar_data_check()
        assert self.fake_services.slack_was_sent_containing('might need to upgrade')
        assert not self.sim_plan_upgraded(subscription)

    def sim_plan_upgraded(self, subscription):
        return subscription.latest_sim.provider_plan_override is None and self.fake_services.cdr_db.sim_is_on_plan(subscription.latest_sim, 'TSL_UK_DATA_100GB')

class ReferralTests(TestCase):
    def test_record_new_referral(self):
        referrer = SubscriberFactory()
        new_subscriber = SubscriberFactory()
        credit_amount = 10.00

        referrer.record_new_referral(new_subscriber, credit_amount)

        referral = Referral.objects.first()
        self.assertEqual(Referral.objects.count(), 1)
        self.assertEqual(referral.referrer, referrer)
        self.assertEqual(referral.referred_subscriber, new_subscriber)
        self.assertEqual(referral.referral_code, referrer.referral_code)
        self.assertEqual(referral.credit_applied, credit_amount)

class TestDailyPointAllocation(test_utils.NexusTestCase):
    def setUp(self):
        super().setUp()
        self.test_client = ClientFactory()
        self.subscription_with_sim = SubscriptionFactory(
            using=True,
            subscriber__client=self.test_client,
            subscriber__perk_points=0,
            subscriber__total_points_earned=0
        )
        self.subscriber_with_sim = self.subscription_with_sim.subscriber
        self.subscription_without_sim = SubscriptionFactory(
            subscriber__client=self.test_client,
            subscriber__perk_points=0,
            subscriber__total_points_earned=0
        )
        self.subscriber_without_sim = self.subscription_without_sim.subscriber
    @override_settings(SEND_SIMP_NOTIFICATIONS=True)
    def test_points_allocation_with_notifications(self):
        points = 100
        self.test_client.allocate_points_to_all_subscribers(points)
        self.subscriber_with_sim.refresh_from_db()
        self.assertEqual(self.subscriber_with_sim.perk_points, points)
        self.assertEqual(self.subscriber_with_sim.total_points_earned, points)
        notification = self.subscriber_with_sim.notifications.first()
        self.assertIsNotNone(notification)
        self.assertEqual(
            notification.text,
            f'You earned {points} {self.test_client.perk_point_name_plural}!'
        )
        self.subscriber_without_sim.refresh_from_db()
        self.assertEqual(self.subscriber_without_sim.perk_points, 0)
        self.assertEqual(self.subscriber_without_sim.total_points_earned, 0)
        self.assertEqual(self.subscriber_without_sim.notifications.count(), 0)

    @override_settings(SEND_SIMP_NOTIFICATIONS=False)
    def test_points_allocation_without_notifications(self):
        points = 100
        self.test_client.allocate_points_to_all_subscribers(points)
        self.subscriber_with_sim.refresh_from_db()
        self.assertEqual(self.subscriber_with_sim.perk_points, points)
        self.assertEqual(self.subscriber_with_sim.total_points_earned, points)
        self.assertEqual(self.subscriber_with_sim.notifications.count(), 0)
        self.subscriber_without_sim.refresh_from_db()
        self.assertEqual(self.subscriber_without_sim.perk_points, 0)
        self.assertEqual(self.subscriber_without_sim.total_points_earned, 0)
        self.assertEqual(self.subscriber_without_sim.notifications.count(), 0)

    def test_point_allocation_creates_allocation_record(self):
        points = 100
        self.test_client.allocate_points_to_all_subscribers(points)
        allocation = PerkPointAllocation.objects.filter(
            subscription=self.subscription_with_sim
        ).first()
        self.assertIsNotNone(allocation)
        self.assertEqual(allocation.amount, points)

    def test_points_only_allocated_to_primary_subscriptions(self):
        points = 100
        subscriber = SubscriberFactory(
            client=self.test_client,
            perk_points=0,
            total_points_earned=0
        )
        primary_subscription = SubscriptionFactory(
            using=True,
            subscriber=subscriber,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.ACTIVE,
            billing_subscription_id='primary_sub_123'
        )
        roaming_subscription = SubscriptionFactory(
            using=True,
            subscriber=subscriber,
            subscription_type=Subscription.SubscriptionTypes.ROAMING_ESIM,
            status=Subscription.Statuses.ACTIVE,
            billing_subscription_id='roaming_sub_123'
        )
        self.test_client.allocate_points_to_all_subscribers(points)
        subscriber.refresh_from_db()
        self.assertEqual(subscriber.perk_points, points)
        self.assertEqual(subscriber.total_points_earned, points)
        primary_allocation = PerkPointAllocation.objects.filter(
            subscription=primary_subscription
        ).first()
        self.assertIsNotNone(primary_allocation)
        self.assertEqual(primary_allocation.amount, points)
        roaming_allocation = PerkPointAllocation.objects.filter(
            subscription=roaming_subscription
        ).first()
        self.assertIsNone(roaming_allocation)

    def test_multiple_subscribers_with_mixed_subscription_types(self):
        points = 50
        subscriber1 = SubscriberFactory(
            client=self.test_client,
            perk_points=0,
            total_points_earned=0
        )
        primary_sub1 = SubscriptionFactory(
            using=True,
            subscriber=subscriber1,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.ACTIVE,
            billing_subscription_id='primary_sub1_456'
        )
        subscriber2 = SubscriberFactory(
            client=self.test_client,
            perk_points=0,
            total_points_earned=0
        )
        roaming_sub2 = SubscriptionFactory(
            using=True,
            subscriber=subscriber2,
            subscription_type=Subscription.SubscriptionTypes.ROAMING_ESIM,
            status=Subscription.Statuses.ACTIVE,
            billing_subscription_id='roaming_sub2_456'
        )
        subscriber3 = SubscriberFactory(
            client=self.test_client,
            perk_points=0,
            total_points_earned=0
        )
        primary_sub3 = SubscriptionFactory(
            using=True,
            subscriber=subscriber3,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.ACTIVE,
            billing_subscription_id='primary_sub3_456'
        )
        roaming_sub3 = SubscriptionFactory(
            using=True,
            subscriber=subscriber3,
            subscription_type=Subscription.SubscriptionTypes.ROAMING_ESIM,
            status=Subscription.Statuses.ACTIVE,
            billing_subscription_id='roaming_sub3_456'
        )
        self.test_client.allocate_points_to_all_subscribers(points)
        subscriber1.refresh_from_db()
        subscriber2.refresh_from_db()
        subscriber3.refresh_from_db()
        self.assertEqual(subscriber1.perk_points, points)
        self.assertEqual(subscriber1.total_points_earned, points)
        self.assertEqual(subscriber2.perk_points, 0)
        self.assertEqual(subscriber2.total_points_earned, 0)
        self.assertEqual(subscriber3.perk_points, points)
        self.assertEqual(subscriber3.total_points_earned, points)
        self.assertTrue(PerkPointAllocation.objects.filter(subscription=primary_sub1).exists())
        self.assertFalse(PerkPointAllocation.objects.filter(subscription=roaming_sub2).exists())
        self.assertTrue(PerkPointAllocation.objects.filter(subscription=primary_sub3).exists())
        self.assertFalse(PerkPointAllocation.objects.filter(subscription=roaming_sub3).exists())

    def test_inactive_primary_subscriptions_dont_get_points(self):
        points = 75
        subscriber = SubscriberFactory(
            client=self.test_client,
            perk_points=0,
            total_points_earned=0
        )
        inactive_primary = SubscriptionFactory(
            using=True,
            subscriber=subscriber,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.INACTIVE,
            billing_subscription_id='inactive_primary_789'
        )
        self.test_client.allocate_points_to_all_subscribers(points)
        subscriber.refresh_from_db()
        self.assertEqual(subscriber.perk_points, 0)
        self.assertEqual(subscriber.total_points_earned, 0)
        allocation = PerkPointAllocation.objects.filter(
            subscription=inactive_primary
        ).first()
        self.assertIsNone(allocation)

class AdditionalSubscriptionDiscountTests(TestCase):
    def setUp(self):
        self.client = ClientFactory()
        self.plan = PlanFactory(client=self.client)
        self.discount = PlanDiscountFactory(
            discount_type=PlanDiscount.DiscountType.PERCENTAGE,
            discount_percentage=20
        )
        self.client.additional_subscription_discount = self.discount
        self.client.save()

    def test_no_discount_on_first_subscription(self):
        subscriber = SubscriberFactory(client=self.client)
        flat_discounts, percentage_discounts = self.plan.get_discounts_applicable_to_new_subscribers(subscriber)
        self.assertEqual(len(flat_discounts), 0)
        self.assertEqual(len(percentage_discounts), 0)

    def test_discount_on_second_subscription(self):
        subscriber = SubscriberFactory(client=self.client)
        _ = SubscriptionFactory(
            subscriber=subscriber,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.ACTIVE
        )
        flat_discounts, percentage_discounts = self.plan.get_discounts_applicable_to_new_subscribers(subscriber)
        self.assertEqual(len(flat_discounts), 0)
        self.assertEqual(len(percentage_discounts), 1)
        self.assertEqual(percentage_discounts[0], self.discount)

    def test_no_discount_when_client_has_no_discount_set(self):
        self.client.additional_subscription_discount = None
        self.client.save()
        subscriber = SubscriberFactory(client=self.client)
        _ = SubscriptionFactory(
            subscriber=subscriber,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.ACTIVE
        )
        flat_discounts, percentage_discounts = self.plan.get_discounts_applicable_to_new_subscribers(subscriber)
        self.assertEqual(len(flat_discounts), 0)
        self.assertEqual(len(percentage_discounts), 0)
