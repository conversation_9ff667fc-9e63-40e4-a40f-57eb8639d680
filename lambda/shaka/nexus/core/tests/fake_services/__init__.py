import requests_mock
from .cdr_db import FakeCDRDB
from .telco import <PERSON>akeTelco
from .slack import FakeSlack
from .stripe import FakeStripe, TestStripe
from .process_warden import FakeProcessWarden

class FakeServices:
    def __init__(self, stripe_backend=''):
        self.telco = FakeTelco()
        self.cdr_db = FakeCDRDB(self.telco)
        self.slack = FakeSlack()
        self.process_warden = FakeProcessWarden()
        self.request_mocker = None
        if stripe_backend == 'fake':
            self.stripe = FakeStripe()
        elif stripe_backend == 'test':
            self.stripe = TestStripe()
        else:
            self.stripe = None

    @property
    def _services(self):
        return [self.cdr_db, self.telco, self.slack, self.process_warden, self.stripe]

    def set_up(self):
        self.request_mocker = requests_mock.Mocker()
        self.request_mocker.start()
        for service in self._services:
            if service:
                service.mock_requests(self.request_mocker)

    def tear_down(self):
        for service in self._services:
            if service:
                if hasattr(service, 'tear_down'):
                    service.tear_down()
        self.request_mocker.stop()

    def sim_is_barred(self, sim_serial):
        return self.telco.sim_is_barred(sim_serial)

    def slack_was_sent(self, message):
        return self.slack.message_was_sent(message)

    def slack_was_sent_containing(self, message):
        return self.slack.message_was_sent_containing(message)

    def no_slack_was_sent(self):
        return self.slack.no_message_was_sent()
