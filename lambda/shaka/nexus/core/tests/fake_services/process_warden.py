import re

class FakeProcessWarden:
    def __init__(self):
        self.messages = []

    def mock_requests(self, requests_mocker):
        api_url = re.compile('https://processwarden.com/tubes/.*')
        requests_mocker.register_uri(
            'POST',
            api_url,
            text=self.record_pw_post
        )

    def record_pw_post(self, request, _):
        parts = [p for p in request.path.split('/') if p]
        _, tube_id, tube_name, _ = parts
        self.messages.append({'tube_id': tube_id, 'tube_name': tube_name, 'json': request.json()})
