import csv
import os
import tempfile
from decimal import Decimal
from datetime import timedelta
from django.core.management import call_command
from django.db.models import Sum
from core import test_utils
from core.tests.factories import ClientFactory, PlanFactory, SubscriptionFactory, SubscriptionPaymentFactory
from core.models import SubscriptionPayment
from core.time_control import STRIPE_BILLING_TIME_CONTROL

class ReverseInvoicingTests(test_utils.NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client = ClientFactory()
        self.plan = PlanFactory(client=self.client, profit_12=True)
        self.base_expected_profit = self.plan.cost_to_client
        self.plan_price_ex_vat = self.plan.price / Decimal('1.2')
        self.plan_vat = self.plan.price - self.plan_price_ex_vat
        self.base_expected_invoice = self.plan_price_ex_vat - self.base_expected_profit

    def generate_invoice(self):
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            tf = f.name
            call_command('draft_client_invoice', '--client-id', str(self.client.id), '--csv-file', tf)
            with open(tf, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                total_invoiced = sum(Decimal(row['prorated_charge']) for row in reader)
        os.remove(tf)
        return Decimal(total_invoiced), Decimal(SubscriptionPayment.objects.filter(subscription__subscriber__client=self.client).aggregate(Sum('amount'))['amount__sum'] - total_invoiced)

    def create_payment_for_last_month(self, subscription, amount=None, date=None):
        if amount is None:
            amount = self.plan.price
        return SubscriptionPaymentFactory(subscription=subscription, amount=amount, date=date)

    def create_subscription_and_payment(self, start_date=None, amount=None):
        if start_date is None:
            start_date = STRIPE_BILLING_TIME_CONTROL.last_month_datetimes[0]
        subscription = SubscriptionFactory(subscriber__client=self.client, plan=self.plan, start_date=start_date)
        sim = subscription.latest_sim
        sim.activation_date = start_date
        sim.save()
        self.create_payment_for_last_month(subscription, amount=amount, date=start_date)
        return subscription

    def test_ordinary_invoice_values(self):
        self.create_subscription_and_payment(amount=self.plan.price)
        total_invoiced, money_to_us = self.generate_invoice()
        assert round(total_invoiced, 2) == round(self.base_expected_invoice, 2), f'Got {total_invoiced} expected {self.base_expected_invoice}'
        assert round(money_to_us, 2) == round(self.plan_vat + self.plan.cost_to_client, 2), f'Got {money_to_us} expected {self.plan_vat + self.plan.cost_to_client}'

    def test_discounted_invoice_values(self):
        discount = 5
        self.create_subscription_and_payment(amount=self.plan.price - discount)
        total_invoiced, money_to_us = self.generate_invoice()
        discounted_price_ex_vat = (self.plan.price - discount) / Decimal("1.2")
        assert total_invoiced == discounted_price_ex_vat - self.plan.cost_to_client, f'Got {total_invoiced} expected {discounted_price_ex_vat - self.plan.cost_to_client}'
        assert round(money_to_us, 2) == round(self.plan.cost_to_client + (self.plan.price - discount - discounted_price_ex_vat), 2)

    def test_prorated_invoice_values(self):
        billing_start, billing_end = STRIPE_BILLING_TIME_CONTROL.last_month_datetimes
        basis_date = billing_start + timedelta(days=round((billing_end - billing_start).days) / 2)
        subscription_seconds = (basis_date - billing_start).total_seconds()
        seconds_this_month = (billing_end - billing_start).total_seconds()
        proration_multiplier = Decimal(subscription_seconds) / Decimal(seconds_this_month)
        self.create_subscription_and_payment(start_date=basis_date, amount=self.plan.price * (1 - proration_multiplier))
        total_invoiced, money_to_us = self.generate_invoice()
        expected_invoice = (self.plan_price_ex_vat - self.plan.cost_to_client) * (1 - proration_multiplier)
        assert round(total_invoiced, 2) == round(expected_invoice, 2), f'Got {total_invoiced} expected {expected_invoice}'
        expected_money_to_us = (self.plan_vat + self.plan.cost_to_client) * (1 - proration_multiplier)
        assert round(money_to_us, 2) == round(expected_money_to_us, 2), f'Got {money_to_us} expected {expected_money_to_us}'

    def test_prorated_discounted_invoice_values(self):
        discount = 3
        billing_start, billing_end = STRIPE_BILLING_TIME_CONTROL.last_month_datetimes
        basis_date = billing_start + timedelta(days=15)
        subscription_seconds = (basis_date - billing_start).total_seconds()
        seconds_this_month = (billing_end - billing_start).total_seconds()
        proration_multiplier = Decimal(subscription_seconds) / Decimal(seconds_this_month)
        self.create_subscription_and_payment(start_date=basis_date, amount=(self.plan.price - discount) * (1 - proration_multiplier))
        total_invoiced, money_to_us = self.generate_invoice()
        expected_invoice = ((self.plan.price - discount) / Decimal('1.2') - self.plan.cost_to_client) * (1 - proration_multiplier)
        assert round(total_invoiced, 2) == round(expected_invoice, 2), f'Got {total_invoiced} expected {expected_invoice}'
        expected_money_to_us = (((self.plan.price - discount) / Decimal('1.2') * Decimal('0.2')) + self.plan.cost_to_client) * (1 - proration_multiplier)
        assert round(money_to_us, 2) == round(expected_money_to_us, 2), f'Got {money_to_us} expected {expected_money_to_us}'
