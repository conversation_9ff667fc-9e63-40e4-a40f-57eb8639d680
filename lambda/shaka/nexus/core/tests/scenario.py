from datetime import timed<PERSON>ta
from django.utils import timezone
from core.utils import gb_to_bytes
from core.models import Sim, PaymentIntegration
from .factories import SimFactory, NumberAssignmentFactory, SimSubscriptionAssignmentFactory, SimPlanAssignmentFactory, SubscriptionFactory, PaymentIntegrationFactory, PlanFactory



def sync_subscription_datetimes(subscription, base_datetime):
    subscription.start_date = base_datetime
    subscription.save()
    sim = subscription.latest_sim
    plan_assignment = sim.latest_plan_assignment
    if plan_assignment:
        plan_assignment.start_date = base_datetime
        plan_assignment.save()
    subscription_assignment = sim.latest_subscription_assignment
    if subscription_assignment:
        subscription_assignment.start_date = base_datetime
        subscription_assignment.save()
    number_assignment = sim.latest_number_assignment
    if number_assignment:
        number_assignment.start_date = base_datetime
        number_assignment.save()


class Scenario:
    def __init__(self, fake_services):
        self.fake_services = fake_services

    def sim_swap(self, subscription, on_date):
        current_sim = subscription.latest_sim
        next_sim = SimFactory()
        NumberAssignmentFactory(sim=next_sim, phone_number=current_sim.latest_msisdn, start_date=on_date)
        SimPlanAssignmentFactory(sim=next_sim, plan=current_sim.latest_plan, start_date=current_sim.latest_plan_assignment.start_date)
        SimSubscriptionAssignmentFactory(sim=next_sim, subscription=subscription, start_date=current_sim.latest_subscription_assignment.start_date)
        number_assignment = current_sim.latest_number_assignment
        number_assignment.end_date = on_date
        number_assignment.save()
        current_sim.status = Sim.SimStatuses.SUSPENDED
        current_sim.save()
        next_sim.activation_date = current_sim.activation_date
        next_sim.save()

    def set_data_usage_this_billing_cycle(self, subscription, usage_gb, number=1, sim=1):
        number = subscription.sim_assignments.order_by('-start_date', '-id')[sim-1].sim.number_assignments.order_by('-start_date', '-id')[number-1].phone_number
        self.fake_services.cdr_db.add_data_usage(number, subscription.current_billing_cycle_start + timedelta(minutes=1), gb_to_bytes(usage_gb))

    def lower_background_plan(self, sim):
        sim.provider_plan_override = 'TSL_UK_DATA_30GB'
        sim.save()

    def start_new_plan(self, subscription, on_date, plan_limit):
        sim = subscription.latest_sim
        new_plan = PlanFactory(client=subscription.subscriber.client, custom_data_limit=plan_limit)
        sim.immediately_move_to_new_plan(new_plan, on_date)

    def setup_billing_for_client(self, client, start_date='2024-01-15'):
        self.fake_services.stripe.start_test_clock(start_date)
        PaymentIntegrationFactory(
            client=client,
            gateway=PaymentIntegration.Gateway.STRIPE,
            public_credentials=self.fake_services.stripe.public_credentials,
            secret_credentials=self.fake_services.stripe.secret_credentials,
            webhook_secret=self.fake_services.stripe.webhook_secret
        )

    def setup_subscription_with_billing(self, start_date='2024-01-15'):
        self.fake_services.stripe.start_test_clock(start_date)
        subscription = SubscriptionFactory(using_mid_tier=True)
        self.setup_billing_for_client(subscription.subscriber.client)
        subscription.latest_plan.sync_to_gateway_if_necessary()
        subscriber = subscription.subscriber
        customer = self.fake_services.stripe.create_customer(subscriber.email)
        subscriber.billing_customer_id = customer.id
        subscriber.save()
        stripe_subscription = self.fake_services.stripe.create_subscription(subscription)
        subscription.billing_subscription_id = stripe_subscription.id
        subscription.save()
        self.fake_services.telco.set_sim_plan(subscription.latest_sim.serial_number, subscription.latest_plan.provider_plan_code)
        base_datetime = timezone.now()
        sync_subscription_datetimes(subscription, base_datetime)
        return subscription
