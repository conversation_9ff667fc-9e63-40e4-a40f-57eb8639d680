from django.urls import include, path

from .router import router
from .views import PlanOfferingSetAPIView, bar_data_for_dangerous_users, NetworkStatisticsAPIView, MonthlyStatementsAPIView, transatel_webhook, stripe_webhook, six_transatel_webhook, MetricAPIView, sms_delivery_report_webhook, test_stripe_webhook, PerkRedemptionAggregateView, SubscriberRedemptionView, gamma_webhook, ClientViewSet, telna_webhook, nordic_webhook, SimAssignmentViewSet, blocklist_event_webhook, LoginView, ChangePasswordView, RefreshTokenView, ForgotPasswordView, ConfirmForgotPasswordView, blank_homepage, daily_tasks

urlpatterns = [
    path('', blank_homepage, name='homepage'),
    path('api/', include(router.urls)),
    path('api/clients/<str:client_id>/', ClientViewSet.as_view({'get': 'retrieve'}), name='clientdetail'),
    path('api/frontend/plan-offering-set/', PlanOfferingSetAPIView.as_view(), name='planofferingset'),
    path('api/frontend/network-statistics/', NetworkStatisticsAPIView.as_view(), name='networkstatis'),
    path('api/frontend/monthly-statements/', MonthlyStatementsAPIView.as_view(), name='monthlystatements'),
    path('api/frontend/metrics/', MetricAPIView.as_view(), name='metrics'),
    path('api/frontend/perks/<str:perk_id>/history/', PerkRedemptionAggregateView.as_view(), name='perkredemptionhistory'),
    path('api/frontend/perks/<str:perk_id>/subscribers/', SubscriberRedemptionView.as_view(), name='perksubscriberredemptions'),
    path('api/frontend/sim-assignment/', SimAssignmentViewSet.as_view(), name='simassignment'),
    path('backend-api/bar-data/', bar_data_for_dangerous_users, name='bar_data_for_dangerous_users'),
    path('backend-api/daily-tasks/', daily_tasks, name='daily_tasks'),
    path('webhooks/transatel/', six_transatel_webhook, name='six_transatel_webhook'),
    path('webhooks/transatel/<str:client_id>/', transatel_webhook, name='transatel_webhook'),
    path('webhooks/gamma/<str:client_id>/', gamma_webhook, name='gamma_webhook'),
    path('webhooks/stripe/test/', test_stripe_webhook, name='test_stripe_webhook'),
    path('webhooks/stripe/<str:pi_id>/', stripe_webhook, name='stripe_webhook'),
    path('webhooks/telna/', telna_webhook, name='telna_webhook'),
    path('webhooks/nordic/', nordic_webhook, name='nordic_webhook'),
    path('webhooks/sms/delivery-report/<str:client_id>/', sms_delivery_report_webhook, name='sms_delivery_report_webhook'),
    path('webhooks/infobip/blocklist/', blocklist_event_webhook, name='blocklist_event_webhook'),
    path('api/auth/login/', LoginView.as_view(), name='login'),
    path('api/auth/change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('api/auth/refresh-token/', RefreshTokenView.as_view(), name='refresh-token'),
    path('api/auth/forgot-password/', ForgotPasswordView.as_view(), name='forgot-password'),
    path('api/auth/confirm-forgot-password/', ConfirmForgotPasswordView.as_view(), name='confirm-forgot-password'),
]
