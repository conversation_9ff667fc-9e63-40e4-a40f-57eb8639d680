import logging
import requests
from django.db import transaction
from .models import SMSMessage, SMSConfiguration, IndividualSMSDelivery


logger = logging.getLogger()
logger.setLevel(logging.INFO)


class SMSClient:
    def send(self, sms):
        raise NotImplementedError()

class FakeSMSClient:
    def send(self, sms):
        count = 0
        recipients = list(sms.get_all_recipients())
        for recipient in sms.get_all_recipients():
            count += 1
            logger.info("Fake SMS sent to %s: %s", recipient, sms.message)
        IndividualSMSDelivery.create_for_recipients(sms, recipients)
        return True

class InfobipSMSClient:
    def send(self, sms):
        config = sms.client.sms_config
        recipients = list(sms.get_all_recipients())
        IndividualSMSDelivery.create_for_recipients(sms, recipients)
        if config.allow_out_of_hours_sending:
            options = {}
        else:
            options = {
                "deliveryTimeWindow": {
                    "days": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
                    "from": {
                        "hour": 9,
                        "minute": 0
                    },
                    "to": {
                        "hour": 17,
                        "minute": 0
                    }
                }
            }
        payload = {
            "messages": [
                {
                    "sender": config.sender,
                    "destinations": [
                        {
                            "to": recipient,
                        }
                        for recipient in recipients
                    ],
                    "content": {
                        "text": sms.message # Brand new Offer from Yayzi Mobile! Click here [insert link] to get an EXTRA 5GB of data for ONLY £1! Stay connected without breaking the bank. Limited time offer, don’t miss!  To opt-out, reply STOP or visit [unsubscribe_link].
                    },
                    "options": options,
                    "webhooks": {
                        "delivery": {
                            "url": config.delivery_report_url,
                            "intermediateReport": True,
                            "notify": True,
                        },
                        "contentType": "application/json",
                        "callbackData": f"{sms.pk}"
                    }
                }
            ]
        }
        headers = {
            'Authorization': f'App {config.credential}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        response = requests.post(f'{config.endpoint}/sms/3/messages', json=payload, headers=headers, timeout=10)
        logger.info('SMS sent: %s', payload)
        logger.info('SMS response: %s', response.text)
        print(response.text)
        # {"bulkId":"1716477839836000011","messages":[{"messageId":"1716477839842000012","status":{"groupId":1,"groupName":"PENDING","id":26,"name":"PENDING_ACCEPTED","description":"Message sent to next instance"},"destination":"447533209633"}]}



def _send_pending_sms(sms):
    if sms.status != SMSMessage.Status.TO_SEND:
        logger.error("Sms is not in to sendstate.")
        return False

    try:
        locked_sms = None
        with transaction.atomic():
            locked_sms = SMSMessage.objects.select_for_update(skip_locked=True).filter(
                pk=sms.pk,
                status=SMSMessage.Status.TO_SEND
            ).first()

            if not locked_sms:
                logger.info("Sms either locked or not in to send state anymore.")
                return False
            locked_sms.status = SMSMessage.Status.SENDING
            locked_sms.save()

        # Attempt to send the sms; errors will be caught by the exception handler
        _actually_send_sms(locked_sms)
        locked_sms.status = SMSMessage.Status.SENT
        locked_sms.save()
        return True

    except Exception as e: # pylint: disable=broad-except
        if locked_sms:
            locked_sms.status = SMSMessage.Status.ERRORED
            locked_sms.save()
            logger.exception("Failed to send sms due to an exception: %s", e)
        else:
            logger.exception("Exception occurred while sending sms and no sms was locked: %s", e)
        return False

def send_any_sms(client):
    all_sms = SMSMessage.objects.filter(
        status=SMSMessage.Status.TO_SEND,
        client=client
    )
    for sms in all_sms:
        if sms.should_send_now():
            _send_pending_sms(sms)


def _actually_send_sms(sms):
    client = {
        SMSConfiguration.SMSProvider.FAKE: FakeSMSClient,
        SMSConfiguration.SMSProvider.INFOBIP: InfobipSMSClient,
    }[sms.client.sms_config.provider]()
    client.send(sms)
    return True
