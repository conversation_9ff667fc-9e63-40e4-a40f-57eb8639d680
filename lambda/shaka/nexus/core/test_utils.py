from datetime import datetime, timed<PERSON><PERSON>
from contextlib import contextmanager
from django.test import TestCase
from core.tests.fake_services import FakeServices
from core.tests.scenario import Scenario
from core.tests.factories import ClientFactory, DashboardUserFactory, ProviderFactory
from core.views import CustomJWTPermission
from .models import Client, Provider


def create_provider(name='gamma'):
    return Provider.objects.create(name=name)

def create_client(name, user_pool_id='user_pool', auth_app_client_id='auth_app_id', auth_app_domain_prefix='auth_app_doman', auth_app_redirect_uri='http://localhost/auth_app_redirect'):
    return Client.objects.create(
        name=name,
        provider=create_provider(),
        user_pool_id=user_pool_id,
        auth_app_client_id=auth_app_client_id,
        auth_app_domain_prefix=auth_app_domain_prefix,
        auth_app_redirect_uri=auth_app_redirect_uri
    )

def halfway_through_billing_cycle():
    date_range = ProviderFactory().current_billing_month_date_range
    return date_range[0] + (date_range[1] - date_range[0]) / 2

def three_quarters_through_billing_cycle():
    date_range = ProviderFactory().current_billing_month_date_range
    return date_range[0] + (date_range[1] - date_range[0]) * 3 / 4

def halfway_through_last_billing_cycle():
    date_range = ProviderFactory().time_control.last_month_datetimes
    return date_range[0] + (date_range[1] - date_range[0]) / 2

def start_of_last_billing_cycle():
    return ProviderFactory().time_control.last_month_datetimes[0]

def future_date_str():
    return (datetime.now() + timedelta(days=1)).isoformat()


class NexusTestCase(TestCase):
    stripe_backend = ''

    def setUp(self):
        self.fake_services = FakeServices(stripe_backend=self.stripe_backend)
        self.fake_services.set_up()
        self.scenario = Scenario(self.fake_services)

    def tearDown(self):
        self.fake_services.tear_down()

    def sim_is_barred(self, sim):
        return sim.is_data_barred and self.fake_services.sim_is_barred(sim.serial_number)


class DashboardAPITestCase(TestCase):
    def setUp(self):
        self.client = ClientFactory()
        self.dashboard_user = DashboardUserFactory(client=self.client)

    @contextmanager
    def forced_auth_dashboard_user(self):
        with MonkeyPatchDashboardUserAuth(self.dashboard_user):
            yield

class MonkeyPatchDashboardUserAuth:
    def __init__(self, dashboard_user):
        self.dashboard_user = dashboard_user
        self.old_has_permission = None

    def __enter__(self):
        self.old_has_permission = getattr(CustomJWTPermission, 'has_permission')
        setattr(CustomJWTPermission, 'has_permission', self.has_permission)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        setattr(CustomJWTPermission, 'has_permission', self.old_has_permission)

    def has_permission(self, request, _):
        request.client_id = self.dashboard_user.client_id
        request.shaka_user = self.dashboard_user
        return True
