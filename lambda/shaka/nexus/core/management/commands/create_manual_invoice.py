from django.core.management.base import BaseCommand, CommandError
from django.utils.dateparse import parse_date
import decimal
from core.models import Client
from core.utils import get_stripe_client

class Command(BaseCommand):
    help = 'Create a manual invoice for a client'

    def add_arguments(self, parser):
        parser.add_argument('--client-id', type=int, help='The ID of the client')
        parser.add_argument('--customer-billing-id', type=str, help='The stripe customer billing id')
        parser.add_argument('--amount', type=decimal.Decimal, help='The amount of the invoice')
        parser.add_argument('--date', type=str, help='The date of the invoice (YYYY-MM-DD)')
        parser.add_argument('--description', type=str, help='The description of the invoice')
        parser.add_argument('--invoice-item-description', type=str, help='The description of the invoice item')

    def handle(self, *args, **options):
        client_id = options['client_id']
        customer_billing_id = options['customer_billing_id']
        amount = options['amount']
        date = options['date']
        description = options['description']
        invoice_item_description = options['invoice_item_description']
        stripe_invoice, invoice_item = create_manual_invoice(client_id, customer_billing_id, amount, date, description, invoice_item_description)
        print(stripe_invoice)
        print(invoice_item)
            

def create_manual_invoice(client_id, customer_billing_id, amount, date_str, description, invoice_item_description):
    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        raise CommandError(f'Client with ID {client_id} does not exist')

    date = parse_date(date_str)
    if date is None:
        raise CommandError(f'Invalid date format: {date_str}')

    stripe_client = get_stripe_client(client.payment_integration.secret_credentials)
    stripe_invoice = stripe_client.invoices.create(
        params={'customer': customer_billing_id,
                'auto_advance':True,
                'collection_method':'charge_automatically',
                'description':description,
                })
    invoice_item = stripe_client.invoice_items.create(
        params=dict(
        customer=customer_billing_id,
        amount=int(amount*100),
        description=invoice_item_description,
        invoice=stripe_invoice.id)
    )
    return stripe_invoice, invoice_item
