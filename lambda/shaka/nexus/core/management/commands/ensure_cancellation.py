# Script to ensure cancellation of a service
# There are generally two cases - one is where we know they've cancelled and want to belt-and-braces the stripe subscription and the service
# The other is where they've pac'd out and we want to just cancel the stripe subscription

from django.core.management.base import BaseCommand, CommandError
from core.utils import get_stripe_client
from core.models import Subscription, PlanChange
from .utils import lookup_subscription_by_iccid


class Command(BaseCommand):
    help = 'Ensure that a subscription is cancelled'

    def add_arguments(self, parser):
        parser.add_argument('--subscription-id', type=int, help='Subscription ID to cancel')
        parser.add_argument('--iccid', type=str, help='ICCID to cancel')

    def handle(self, *args, **options):
        if options['subscription_id'] is None and options['iccid'] is None:
            raise CommandError('You must provide a subscription ID or ICCID')

        if options['subscription_id'] and options['iccid']:
            raise CommandError('You must provide either a subscription ID or ICCID, not both')

        if options['subscription_id']:
            self.cancel_by_subscription_id(options['subscription_id'])
        else:
            self.cancel_by_iccid(options['iccid'])

    def cancel_by_iccid(self, iccid):
        # Suspected pac out
        self.ensure_potential_pac_out_iccid_still_has_active_subscription(iccid)
        self.cancel_stripe_subscription(lookup_subscription_by_iccid(iccid))

    def ensure_potential_pac_out_iccid_still_has_active_subscription(self, iccid):
        subs = lookup_subscription_by_iccid(iccid)
        if not subs.is_active:
            raise CommandError(f'Subscription {subs} is not active, check for cancellation logic instead')

    def cancel_by_subscription_id(self, subscription_id):
        subscription = Subscription.objects.get(pk=subscription_id)
        self.ensure_has_active_service(subscription)
        self.ensure_has_planned_cancellation(subscription)
        self.cancel_stripe_subscription(subscription)
        self.cancel_service(subscription)

    def ensure_has_active_service(self, subscription):
        if not subscription.is_active:
            raise CommandError(f'Subscription {subscription} is not active, check for cancellation logic instead')
        if not subscription.latest_sim:
            raise CommandError(f'Subscription {subscription} does not have an active service')
        if not subscription.latest_sim.is_active:
            raise CommandError(f'Subscription {subscription} has a service that is not active')

    def ensure_has_planned_cancellation(self, subscription):
        qs = subscription.plan_changes.filter(status=PlanChange.Status.IN_PROGRESS, change_type=PlanChange.ChangeType.CANCELLATION)
        if not qs.exists():
            raise CommandError(f'Subscription {subscription} does not have a planned cancellation')
        if qs.count() > 1:
            raise CommandError(f'Subscription {subscription} has multiple planned cancellations')
        plan_change = qs.first()
        if PlanChange.objects.filter(target_plan_change=plan_change).exists():
            raise CommandError(f'Subscription {subscription} has a planned cancellation with a target plan change')

    def cancel_stripe_subscription(self, subscription):
        stripe_client = get_stripe_client(subscription.subscriber.client.payment_integration.secret_credentials)
        stripe_subscription = stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
        if stripe_subscription['status'] != 'canceled':
            stripe_subscription.delete()
        subscription.status = Subscription.Statuses.CANCELLED
        subscription.save()

    def cancel_service(self, subscription):
        subscription.subscriber.client.provider.cancel_service(subscription.latest_sim.serial_number)
        print(f'Subscription {subscription} has been cancelled')
