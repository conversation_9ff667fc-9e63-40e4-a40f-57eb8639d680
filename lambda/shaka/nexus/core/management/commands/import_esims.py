import csv
from django.core.management.base import BaseCommand
from core.models import Sim

class Command(BaseCommand):
    help = 'Import SIM data from a CSV file'

    def add_arguments(self, parser):
        parser.add_argument('csv_file', type=str, help='The path to the CSV file to import')
        parser.add_argument('client_id', type=str, help='The client id for the sims')

    def handle(self, *args, **kwargs):
        csv_file_path = kwargs['csv_file']
        
        sims_to_create = []

        try:
            with open(csv_file_path) as csvfile:
                reader = csv.DictReader(csvfile)
                
                for row in reader:
                    sim = Sim(
                        serial_number=row['ICCID'].strip().replace('F', ''),
                        esim_data=row['ACTIVATIONCODE'].strip(),
                        esim_available_to_id=kwargs['client_id'],
                    )
                    sims_to_create.append(sim)

            Sim.objects.bulk_create(sims_to_create)
            self.stdout.write(self.style.SUCCESS(f'Successfully imported {len(sims_to_create)} SIM records'))

        except FileNotFoundError:
            self.stderr.write(self.style.ERROR(f'File "{csv_file_path}" not found'))
        except csv.Error as e:
            self.stderr.write(self.style.ERROR(f'Error reading CSV file: {e}'))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f'An error occurred: {e}'))
