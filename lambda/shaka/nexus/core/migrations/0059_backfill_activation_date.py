# Generated by Django 4.2.7 on 2024-05-15 12:18

from django.db import migrations


def backfill_activation_date(apps, schema_editor):
    Sim = apps.get_model('core', 'Sim')
    WebhookActionLog = apps.get_model('core', 'WebhookActionLog')

    activation_dates_by_sim_serial = {}
    for webhook_action_log in WebhookActionLog.objects.filter(status='completed', webhook_log__webhook_source='transatel'):
        event_type = webhook_action_log.webhook_log.payload['header']['eventType']
        if event_type == 'CONNECTIVITY-MANAGEMENT/SUBSCRIBER/ACTIVATED':
            activation_dates_by_sim_serial[webhook_action_log.webhook_log.payload['body']['simSerial']] = webhook_action_log.webhook_log.date
    
    for sim in Sim.objects.all():
        if sim.status == 'active' and sim.activation_date is None:            
            sim.activation_date = activation_dates_by_sim_serial.get(sim.serial_number)
            if sim.activation_date is not None:
                sim.save()

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0058_sim_activation_date'),
    ]

    operations = [
        migrations.RunPython(backfill_activation_date)
    ]
