# Generated by Django 4.2.7 on 2024-08-04 23:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0091_plan_bg'),
    ]

    operations = [
        migrations.AddField(
            model_name='provider',
            name='is_demo',
            field=models.BooleanField(default=False, help_text='Whether this provider is a demo provider'),
        ),
        migrations.CreateModel(
            name='DemoUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension', models.CharField(choices=[('sms', 'SMS'), ('voice', 'Voice'), ('data', 'Data')], max_length=10)),
                ('value', models.FloatField()),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
                ('sim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.sim')),
            ],
        ),
    ]
