# Generated by Django 4.2.7 on 2024-02-11 19:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0015_plan_plan_key_plan_version_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='plan',
            name='data_bar_or_upgrade_threshold_override',
            field=models.FloatField(blank=True, help_text='Bar or upgrade threshold override in gb, default 95%', null=True),
        ),
        migrations.AddField(
            model_name='plan',
            name='data_warning_threshold_override',
            field=models.FloatField(blank=True, help_text='Warning threshold override in gb, default 80%', null=True),
        ),
        migrations.AddField(
            model_name='sim',
            name='is_data_barred',
            field=models.BooleanField(default=False, help_text='Temporary kludge until we can determine barring status from API'),
        ),
    ]
