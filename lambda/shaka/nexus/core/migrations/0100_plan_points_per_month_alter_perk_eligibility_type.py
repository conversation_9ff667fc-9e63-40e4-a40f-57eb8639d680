# Generated by Django 4.2.7 on 2024-08-15 11:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0099_bolton_boltonoffering_clientbolton_boltonpurchase'),
    ]

    operations = [
        migrations.AddField(
            model_name='plan',
            name='points_per_month',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='perk',
            name='eligibility_type',
            field=models.CharField(choices=[('tenure', 'Tenure'), ('total_spend', 'Total Spend'), ('total_points_earned', 'Total Points Earned'), ('airdrop', 'Airdrop'), ('no_free', 'No Free')], max_length=40),
        ),
    ]
