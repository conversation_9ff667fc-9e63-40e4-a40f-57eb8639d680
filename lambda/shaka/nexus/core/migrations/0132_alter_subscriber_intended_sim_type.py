# Generated by Django 4.2.7 on 2024-10-30 10:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0131_client_auth_app_client_secret'),
    ]

    operations = [
        migrations.AlterField(
            model_name='subscriber',
            name='intended_sim_type',
            field=models.CharField(blank=True, choices=[('physical', 'Physical'), ('esim', 'eSIM')], help_text='The type of SIM the subscriber intends to use (stored for the signup flow) - this belongs on the subscription for multi-subscription but for now it can live here', max_length=10, null=True),
        ),
    ]
