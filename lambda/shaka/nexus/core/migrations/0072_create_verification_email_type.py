# Generated by Django 4.2.7 on 2024-06-06 17:49

from django.db import migrations

def add_email_type(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.create(name='verify-subscriber')

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0071_emailconfiguration_client_logo_url_and_more'),
    ]

    operations = [
        migrations.RunPython(add_email_type)
    ]
