# Generated by Django 4.2.7 on 2024-11-06 14:49

from django.db import migrations

def add_email_type(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.create(name='welcome-new-customer')
    EmailType.objects.create(name='no-payment')
    EmailType.objects.create(name='esim-activation')
    EmailType.objects.create(name='physical-delivery')
    EmailType.objects.create(name='porting-failed')
    EmailType.objects.create(name='cancellation')
    EmailType.objects.create(name='cancellation-pac')
    EmailType.objects.create(name='cancellation-stac')

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0133_emailconfiguration_help_data_issue_and_more'),
    ]

    operations = [
        migrations.RunPython(add_email_type)
    ]
