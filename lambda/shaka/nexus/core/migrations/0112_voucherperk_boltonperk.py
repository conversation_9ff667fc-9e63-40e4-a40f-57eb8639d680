# Generated by Django 4.2.7 on 2024-09-19 10:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0111_add_esim_email_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='VoucherPerk',
            fields=[
                ('perk_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.perk')),
                ('code', models.CharField(max_length=20)),
                ('url', models.URLField(blank=True, null=True)),
                ('instructions', models.TextField(blank=True, null=True)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('merchant_name', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'abstract': False,
                'base_manager_name': 'objects',
            },
            bases=('core.perk',),
        ),
        migrations.CreateModel(
            name='BoltOnPerk',
            fields=[
                ('perk_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.perk')),
                ('bolt_on', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.clientbolton')),
            ],
            options={
                'abstract': False,
                'base_manager_name': 'objects',
            },
            bases=('core.perk',),
        ),
    ]
