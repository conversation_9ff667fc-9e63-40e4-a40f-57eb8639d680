# Generated by Django 4.2.7 on 2024-10-01 00:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0120_plandiscount_billing_discount_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='plandiscount',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='plandiscount',
            name='campaign',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='plan_discounts', to='core.campaign'),
        ),
        migrations.AlterField(
            model_name='plandiscount',
            name='discount_perk',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='plan_discounts', to='core.discountperk'),
        ),
        migrations.AlterField(
            model_name='plandiscount',
            name='plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='discounts', to='core.plan'),
        ),
        migrations.CreateModel(
            name='DiscountApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('applied_at', models.DateTimeField(auto_now_add=True)),
                ('plan_discount', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discount_applications', to='core.plandiscount')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discount_applications', to='core.subscription')),
            ],
        ),
    ]
