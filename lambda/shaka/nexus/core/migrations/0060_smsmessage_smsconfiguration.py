# Generated by Django 4.2.7 on 2024-05-16 10:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0059_backfill_activation_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='SMSMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.CharField(max_length=200)),
                ('send_on', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('to_send', 'To Send'), ('sending', 'Sending'), ('sent', 'Sent'), ('errored', 'Errored')], default='to_send', max_length=10)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
                ('initiator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.dashboarduser')),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='SMSConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('ses', 'Amazon SES'), ('msg91', 'Message 91')], default='msg91', max_length=10)),
                ('identity', models.CharField(blank=True, max_length=255, null=True)),
                ('credential', models.CharField(blank=True, max_length=255, null=True)),
                ('endpoint', models.CharField(blank=True, max_length=255, null=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
            ],
        ),
    ]
