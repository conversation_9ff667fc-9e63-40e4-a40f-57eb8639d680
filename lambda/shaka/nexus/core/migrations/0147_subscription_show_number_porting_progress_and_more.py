# Generated by Django 4.2.7 on 2024-12-16 17:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0146_client_enable_wallet_checkout'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='show_number_porting_progress',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='subscription',
            name='show_set_up_esim',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='subscription',
            name='show_update_apn',
            field=models.BooleanField(default=False),
        ),
    ]
