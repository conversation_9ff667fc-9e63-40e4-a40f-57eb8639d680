# Generated by Django 4.2.7 on 2024-06-05 11:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0068_rename_failed_smsdeliveryreport_total_failed_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='smsdeliveryreport',
            name='units',
        ),
        migrations.AddField(
            model_name='smsdeliveryreport',
            name='total_units',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='smsdeliveryreport',
            name='units_per_message',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AlterField(
            model_name='smsdeliveryreport',
            name='total_delivered',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='smsdeliveryreport',
            name='total_failed',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='smsdeliveryreport',
            name='total_pending',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='smsdeliveryreport',
            name='total_sent',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
