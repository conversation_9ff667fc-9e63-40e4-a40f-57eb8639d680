from django.utils import timezone
from django.conf import settings
from core.utils import epoch_to_utc
from core.models import Subscription, SubscriptionPayment, BoltOnPurchase, Subscriber, RoamingEsimPackage, RoamingEsimPurchase
from core.slack import send_slack_message
from core.emails import send_welcome_new_customer_email

class BillingClient:
    def __init__(self, client):
        self.client = client

    def get_subscriber(self, billing_id):
        return self.client.subscribers.filter(billing_subscriber_id=billing_id).first()

    def create_subscriber(self, *, billing_id, email, name):
        return self.client.subscribers.create(
            billing_subscriber_id=billing_id,
            join_date=timezone.now(),
            email=email,
            name=name)

    def get_subscription(self, billing_subscription_id):
        return Subscription.objects.filter(subscriber__client=self.client, billing_subscription_id=billing_subscription_id).first()

    def _should_slack(self, subscription):
        return subscription.client.pk > settings.SLACK_CLIENT_PK_CUTOFF and not subscription.client.provider.is_demo

    def create_subscription(self, billing_subscriber_id, billing_subscription_id, start_date_epoch, correlation_id=None):
        subscription = self.get_subscriber(billing_subscriber_id).subscriptions.create(
            billing_subscription_id=billing_subscription_id,
            correlation_id=correlation_id,
            start_date=epoch_to_utc(start_date_epoch))
        if self._should_slack(subscription):
            send_slack_message(f'New subscription created ({subscription.client.name}, {subscription.subscriber.email}), might not be paid yet: [link in nexus](https://nexus.shaka.tel/admin/core/subscription/{subscription.pk}/change/)')
        if self.client.starting_perk_points:
            subscription.subscriber.allocate_points(self.client.starting_perk_points, subscription)
        return subscription


    def pause_subscription(self, billing_id):
        subscription = self.get_subscription(billing_id)
        subscription.status = Subscription.Statuses.INACTIVE
        subscription.save()

    def resume_subscription(self, billing_id):
        subscription = self.get_subscription(billing_id)
        subscription.status = Subscription.Statuses.ACTIVE
        subscription.save()

    # pylint: disable=too-many-arguments
    def mark_subscription_payment(self, *, billing_subscription_id, billing_invoice_id, amount, currency, date_epoch):
        if SubscriptionPayment.objects.filter(billing_invoice_id=billing_invoice_id).exists():
            raise RuntimeError('Duplicate payment?')
        subscription=self.get_subscription(billing_subscription_id)
        payment = SubscriptionPayment.objects.create(
            subscription=subscription,
            amount=amount,
            billing_invoice_id=billing_invoice_id,
            currency=getattr(SubscriptionPayment.Currency, currency.upper()),
            date=epoch_to_utc(date_epoch))
        subscription.maybe_activate_esim(definitely_paid=amount > 0)
        subscription.allocate_prorated_perk_points()
        if SubscriptionPayment.objects.filter(subscription=subscription).count() == 1:
            if self._should_slack(subscription):
                send_slack_message(f'New subscription payment for {subscription.subscriber.email} ({subscription.client.name}), needs sim: [link in nexus](https://nexus.shaka.tel/admin/core/subscription/{subscription.pk}/change/)')
            send_welcome_new_customer_email(subscription.pk, self.client)
        return payment

    def purchase_bolt_on(self, billing_subscription_id, bolt_on_id, price, country_code=None, expiry_timestamp=None, quantity=1):
        bolt_on = self.client.bolt_ons.get(pk=bolt_on_id)
        send_slack_message(f'Bolt-on purchased for {self.client.name}: {bolt_on}')
        BoltOnPurchase.objects.create(
            bolt_on=bolt_on,
            quantity=quantity,
            subscription=self.get_subscription(billing_subscription_id),
            price=price,
            country_code=country_code,
            expiry=self.client.time_control.from_timestamp(expiry_timestamp) if expiry_timestamp else None)

    def is_wrong_side_of_shared_account(self, *,billing_subscription_id=None, billing_subscriber_id=None):
        if self.client.payment_integration.is_shared:
            if billing_subscription_id:
                try:
                    subs = Subscription.objects.get(billing_subscription_id=billing_subscription_id)
                    if subs.client != self.client:
                        return True
                except Subscription.DoesNotExist:
                    pass
            if billing_subscriber_id:
                try:
                    sub = Subscriber.objects.get(billing_subscriber_id=billing_subscriber_id)
                    if sub.client != self.client:
                        return True
                except Subscriber.DoesNotExist:
                    pass
        return False

    def purchase_roaming_package(self, subscriber_billing_id, roaming_package_id, amount_paid, country_code, extend_subscription_id=None, correlation_id=None):
        package = RoamingEsimPackage.objects.get(pk=roaming_package_id)
        subscriber = self.get_subscriber(subscriber_billing_id)
        key_name = None
        for region in package.regions.all():
            if region.name == country_code:
                key_name = region.name
        if not key_name:
            key_name = country_code
        subscription, provider_reference = package.create_or_extend_roaming_esim(subscriber, extend_subscription_id=extend_subscription_id, correlation_id=correlation_id)
        RoamingEsimPurchase.objects.create(
            subscription=subscription,
            package=package,
            amount_paid=amount_paid,
            provider_reference=provider_reference,
            key_name=key_name)
