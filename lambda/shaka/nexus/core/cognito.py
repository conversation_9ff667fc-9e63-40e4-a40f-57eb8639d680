from collections import defaultdict
import secrets
import random
import string
import boto3
import requests

def _generate_random_password():
    characters = string.ascii_letters + string.digits
    password = ''.join(secrets.choice(characters) for _ in range(18)) + "!@"
    upper_case_char = secrets.choice(string.ascii_uppercase)
    password += upper_case_char
    chars = list(password)
    random.shuffle(chars)
    return ''.join(chars)


def create_user_in_cognito(email, client_obj):
    client = boto3.client('cognito-idp', region_name='eu-west-2')
    user_attributes = [
        {'Name': 'email', 'Value': email},
    ]

    user_pool_id = client_obj.user_pool_id
    temp_password = _generate_random_password()
    response = client.admin_create_user(
        Username=email,
        UserPoolId=user_pool_id,
        UserAttributes=user_attributes,
        TemporaryPassword=temp_password,
    )

    client.admin_update_user_attributes(
        UserPoolId=user_pool_id,
        Username=email,
        UserAttributes=[
            {
                'Name': 'email_verified',
                'Value': 'true'
            },
        ]
    )
    client.admin_set_user_password(
        UserPoolId=user_pool_id,
        Username=email,
        Password=temp_password,
        Permanent=True
    )
    return response['User']['Username']


def update_user_in_cognito(email, cognito_username, client_obj):
    client = boto3.client('cognito-idp', region_name='eu-west-2')

    user_attributes = [
        {
            'Name': 'email',
            'Value': email
        },
        {
            'Name': 'email_verified',
            'Value': 'true'
        }
    ]
    response = client.admin_update_user_attributes(
        UserPoolId=client_obj.user_pool_id,
        Username=cognito_username,
        UserAttributes=user_attributes
    )
    return response


class CognitoKeyCache:
    def __init__(self):
        self.cache = defaultdict(list)

    def _get_cache(self, user_pool_id):
        return self.cache[user_pool_id]

    def _fetch_updated_keys(self, user_pool_id):
        url = f'https://cognito-idp.eu-west-2.amazonaws.com/{user_pool_id}/.well-known/jwks.json'
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        self.cache[user_pool_id] = resp.json()['keys']

    def get_matching_signing_key(self, user_pool_id, kid, refresh_keys_on_miss=True):
        for known_key in self._get_cache(user_pool_id):
            if known_key['kid'] == kid:
                return known_key
        if refresh_keys_on_miss:
            self._fetch_updated_keys(user_pool_id)
            return self.get_matching_signing_key(user_pool_id, kid, refresh_keys_on_miss=False)
        return None

cognito_key_cache = CognitoKeyCache()
