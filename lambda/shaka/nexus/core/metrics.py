from enum import Enum
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.utils.timezone import now
from .models import SimPlanAssignment, Client
from .front_cache import get_cache_value, FrontCacheable, register_front_cache


class MetricSubperiod:
    def __init__(self, period, start_date, end_date):
        self.period = period
        self.start_date = start_date
        self.end_date = end_date

    @property
    def human_display(self):
        return f'{self.start_date.strftime("%B %Y")}'

    @property
    def dates(self):
        return self.start_date, self.end_date

    @property
    def is_previous(self):
        return self == self.period.previous_subperiod

    def __eq__(self, other):
        return self.start_date == other.start_date and self.end_date == other.end_date and self.period == other.period

    def __str__(self):
        return f'{self.start_date} - {self.end_date}'

class MetricPeriod:
    def __init__(self, periodicity, start_date, end_date, previous_period_start, previous_previous_period_start):  # pylint: disable=too-many-arguments
        self.periodicity = periodicity
        self.start_date = start_date
        self.end_date = end_date
        self.previous_period_start = previous_period_start
        self.previous_previous_period_start = previous_previous_period_start

    @property
    def periodicity_name(self):
        return self.periodicity.periodicity_name

    @property
    def main_subperiod(self):
        return MetricSubperiod(self, self.start_date, self.end_date)

    @property
    def previous_subperiod(self):
        return MetricSubperiod(self, self.previous_period_start, self.start_date)

    @property
    def previous_previous_subperiod(self):
        return MetricSubperiod(self, self.previous_previous_period_start, self.previous_period_start)

    @property
    def is_now(self):
        return self.end_date >= now()

    @property
    def is_previous_to_now(self):
        return self.previous_period_start >= now()

    @property
    def human_display(self):
        return f'{self.start_date.strftime("%B %Y")}'

    @property
    def future_extrapolation_periods(self):
        return self.periodicity.future_extrapolation_periods

    def __str__(self):
        return f'{self.start_date} - {self.end_date}'

class MetricPeriodicity(Enum):
    WEEKLY = 'weekly'
    MONTHLY = 'monthly'
    YEARLY = 'yearly'

    @property
    def periodicity_name(self):
        return self.name.lower().replace('ly','')

    @property
    def future_extrapolation_periods(self):
        if self == MetricPeriodicity.WEEKLY:
            return 4
        if self == MetricPeriodicity.MONTHLY:
            return 12
        if self == MetricPeriodicity.YEARLY:
            return 5
        raise RuntimeError('No valid period for extrapolation?')

    def get_appropriate_start_date(self, time_control, end_date, num_periods=1):
        base_start_date = end_date
        for _ in range(num_periods):
            base_start_date = time_control.get_previous_period_start_and_end(base_start_date)[0]
        return base_start_date

    def get_appropriate_end_date(self, time_control):
        return time_control.current_month_datetimes[1]

    def get_relative_delta(self):
        if self == MetricPeriodicity.WEEKLY:
            return relativedelta(weeks=1)
        if self == MetricPeriodicity.MONTHLY:
            return relativedelta(months=1)
        if self == MetricPeriodicity.YEARLY:
            return relativedelta(years=1)
        raise RuntimeError('No valid period?')

    def align_date(self, dt):
        if self == MetricPeriodicity.WEEKLY:
            return dt - timedelta(days=dt.weekday())
        if self == MetricPeriodicity.MONTHLY:
            return dt.replace(day=1)
        if self == MetricPeriodicity.YEARLY:
            return dt.replace(day=1, month=1)
        raise RuntimeError('No valid align?')

    def to_metric_period_range(self, time_control, start_date, end_date):
        periods = []
        actual_start = self.align_date(start_date).replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        relative_delta = self.get_relative_delta()

        previous_start = time_control.apply_relative_delta(actual_start, -relative_delta)
        previous_previous_start = time_control.apply_relative_delta(actual_start, -(relative_delta * 2))

        while actual_start < end_date:
            new_end = time_control.apply_relative_delta(actual_start, relative_delta)
            periods.append(MetricPeriod(periodicity=self, start_date=actual_start, end_date=new_end, previous_period_start=previous_start, previous_previous_period_start=previous_previous_start))
            previous_previous_start = previous_start
            previous_start = actual_start
            actual_start = new_end
        return periods


class MetricPeriodValue:
    def __init__(self, period, key, name, values):
        self.period = period
        self.key = key
        self.name = name
        self.values = values

    @property
    def metric_period(self):
        return self.period

    @property
    def start_date(self):
        return self.metric_period.start_date

    @property
    def start_date_iso(self):
        return self.start_date.isoformat()

    @property
    def metric_values(self):
        return self.values


# pylint: disable=too-many-instance-attributes
class MetricValue:
    def __init__(self, *, period, subperiod, value, context, number_format, is_primary, is_extrapolated, description):  # pylint: disable=too-many-arguments
        self.period = period
        self.subperiod = subperiod
        self.value = value
        self.context = context
        self.number_format = number_format
        self.is_primary = is_primary
        self.is_extrapolated = is_extrapolated
        self.description = description


class Metric:
    name = None
    key = None
    number_format = None

    def __init__(self, client, metric_periods):
        self.client = client
        self.metric_periods = metric_periods

    def calculate_period_values(self):
        self._prepare_to_calculate(self.metric_periods)
        for period in self.metric_periods:
            yield self._calculate_period_value(period)

    def _prepare_to_calculate(self, metric_periods):
        pass

    def _create_metric_value(self, *, value, period, subperiod, is_primary=False, is_extrapolated=False, context=None, description=None, number_format=None):  # pylint: disable=too-many-arguments
        if context is None:
            context = self._get_context_for_period(period, subperiod, is_extrapolated)
        if description is None:
            description = self._get_description_for_metric_value(value, period)
        if number_format is None:
            number_format = self.number_format
        return MetricValue(period=period, value=value, subperiod=subperiod, context=context, number_format=number_format, is_primary=is_primary, is_extrapolated=is_extrapolated, description=description)

    def _calculate_period_value(self, period):
        return MetricPeriodValue(period=period, key=self.key, name=self.name, values=self._calculate_simple_metric_values(period))

    def _calculate_simple_metric_values(self, period):
        raise NotImplementedError

    def _get_description_for_metric_value(self, value, period):
        raise NotImplementedError

    def _get_context_for_period(self, period, subperiod, extrapolated):
        if subperiod.is_previous and period.is_now:
            return f'last {period.periodicity_name}'
        elif period.is_now:
            if extrapolated:
                return 'current'
            else:
                return f'{period.periodicity_name} to date'
        else:
            return f'{subperiod.human_display}'

    def _extrapolate_figure(self, figure, period):
        if figure is None:
            return None
        if period.is_now:
            start_date, end_date = period.main_subperiod.dates
            return figure / max(1, (end_date - now()).days * (end_date - start_date).days)
        else:
            return figure

@register_front_cache('plan-assignment-metric')
class PlanAssignmentMetricCache(FrontCacheable):
    def precache(self):
        for client in Client.objects.all():
            periodicity = MetricPeriodicity.MONTHLY
            end_date = periodicity.get_appropriate_end_date(client.time_control)
            start_date = periodicity.get_appropriate_start_date(client.time_control, end_date, 2)
            self.cache(client.id, start_date, end_date)

    def recache(self):
        self.precache()

    def args_to_key(self, client_id, start_date, end_date):  # pylint: disable=arguments-differ
        return f'{client_id}:{start_date.isoformat()}:{end_date.isoformat()}'

    def _get(self, client_id, start_date, end_date):  # pylint: disable=arguments-differ
        if client_id:
            return list(SimPlanAssignment.objects.prefetch_related('plan__client__provider', 'plan__voice_component_offering__plan_component', 'plan__data_component_offering__plan_component', 'plan__sms_component_offering__plan_component', 'plan').date_filter(start_date, end_date).filter(plan__client_id=client_id))
        return []


class PlanAssignmentMetric(Metric):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.plan_assignments = []

    def _prepare_to_calculate(self, metric_periods):
        self.plan_assignments = get_cache_value('plan-assignment-metric', self.client.id, metric_periods[0].start_date, metric_periods[-1].end_date)

    def _assignment_date_is_relevant(self, assignment, start_date, end_date):
        return (assignment.start_date < end_date and assignment.end_date is None) or (assignment.start_date >= start_date and assignment.start_date < end_date) or (assignment.end_date is not None and assignment.end_date >= start_date and assignment.end_date < end_date)

    def _calculate_simple_metric_values(self, period):
        raise NotImplementedError

    def _get_description_for_metric_value(self, value, period):
        raise NotImplementedError


class ProfitGrowthMetric(PlanAssignmentMetric):
    name = 'Growth'
    key = 'profit-growth'
    number_format = 'percentage'

    def _get_profit(self, subperiod):
        start_date, end_date = subperiod.dates
        profit = 0
        for assignment in self.plan_assignments:
            if self._assignment_date_is_relevant(assignment, start_date, end_date):
                profit += assignment.get_prorated_client_profit(start_date, end_date)
        return profit

    def _calculate_growth(self, dates_of_interest, prior_dates):
        old_profit = self._get_profit(prior_dates)
        now_profit = self._get_profit(dates_of_interest)
        if old_profit == 0:
            return None
        return (now_profit - old_profit) / old_profit * 100

    def _get_description_for_metric_value(self, value, period):
        if value is None:
            return 'Your growth rate is calculated by the percentage change of profit within the specific time period. Any value above 1% ensures long term growth.'
        elif value > 1:
            return f'Your profit is growing at a rate of {value:.2f}% per {period.periodicity_name} - this is great, any growth above 1% ensures long term growth'
        elif value > 0:
            return f'Your profit is growing at a rate of {value:.2f}% per {period.periodicity_name} - this is good, but you should aim for growth above 1% for long term growth'
        else:
            return f'Your profit is shrinking at a rate of {value:.2f}% per {period.periodicity_name} - you should aim for growth above 1% for long term growth'

    def _calculate_simple_metric_values(self, period):
        main_subperiod = period.main_subperiod
        previous_subperiod = period.previous_subperiod
        previous_previous_subperiod = period.previous_previous_subperiod
        current_growth = self._calculate_growth(main_subperiod, period.previous_subperiod)
        previous_growth = self._calculate_growth(previous_subperiod, previous_previous_subperiod)
        if period.is_now:
            current_growth = self._extrapolate_figure(current_growth, period)
            extrapolated = True
        else:
            extrapolated = False
        return [
            self._create_metric_value(value=current_growth, is_primary=True, period=period, subperiod=main_subperiod, is_extrapolated=extrapolated),
            self._create_metric_value(value=previous_growth, is_primary=False, period=period, subperiod=previous_subperiod),
        ]


class ProfitPerSubscriberMetric(PlanAssignmentMetric):
    name = 'Profit per user'
    key = 'profit-per-subscriber'
    number_format = 'currency'

    def _get_profit(self, subperiod):
        start_date, end_date = subperiod.dates
        profit = 0
        assigned = 0
        for assignment in self.plan_assignments:
            if self._assignment_date_is_relevant(assignment, start_date, end_date):
                assigned += 1
                profit += assignment.get_prorated_client_profit(start_date, end_date)
        if assigned == 0:
            return None
        return profit / assigned

    def _calculate_profit(self, dates_of_interest):
        now_profit = self._get_profit(dates_of_interest)
        return now_profit

    def _get_description_for_metric_value(self, value, period):
        return 'Your average profit calculation  encompasses profit made across plans and bolt-ons per subscriber. Adding attractive bolt-ons can help increase it.'

    def _calculate_simple_metric_values(self, period):
        main_subperiod = period.main_subperiod
        previous_subperiod = period.previous_subperiod
        current_profit = self._calculate_profit(main_subperiod)
        previous_profit = self._calculate_profit(previous_subperiod)
        extrapolated = period.is_now
        return [
            self._create_metric_value(value=current_profit, is_primary=True, period=period, subperiod=main_subperiod, is_extrapolated=extrapolated),
            self._create_metric_value(value=previous_profit, is_primary=False, period=period, subperiod=previous_subperiod),
        ]


class ProfitPerPeriodMetric(PlanAssignmentMetric):
    name = 'Profit per period'
    key = 'profit-per-period'
    number_format = 'currency'

    def _get_profit(self, subperiod):
        start_date, end_date = subperiod.dates
        profit = 0
        assigned = 0

        for assignment in self.plan_assignments:
            if self._assignment_date_is_relevant(assignment, start_date, end_date):
                assigned += 1
                profit += assignment.get_prorated_client_profit(start_date, end_date)
        if assigned == 0:
            return None
        return profit

    def _calculate_simple_metric_values(self, period):
        main_subperiod = period.main_subperiod
        previous_subperiod = period.previous_subperiod
        current_profit = self._get_profit(main_subperiod)
        previous_profit = self._get_profit(previous_subperiod)
        extrapolated = period.is_now

        return [
            self._create_metric_value(value=current_profit, is_primary=True, period=period, subperiod=main_subperiod, is_extrapolated=extrapolated),
            self._create_metric_value(value=previous_profit, is_primary=False, period=period, subperiod=previous_subperiod),
        ]

    def _get_description_for_metric_value(self, value, period):
        return 'Your profit per period is the total profit made across all subscribers and bolt-ons during the period.'


class LifetimeValueMetric(PlanAssignmentMetric):
    name = 'Lifetime value'
    key = 'lifetime-value'
    number_format = 'currency'

    def _calculate_simple_metric_values(self, period):
        start_date = now() - relativedelta(years=10)
        end_date = period.end_date
        if len(self.plan_assignments) == 0:
            return [self._create_metric_value(value=None, is_primary=True, period=period, subperiod=period.main_subperiod)]
        profit = 0
        total_duration = 0
        for assignment in self.plan_assignments:
            if self._assignment_date_is_relevant(assignment, start_date, end_date):
                profit += assignment.get_prorated_client_profit(start_date, end_date)
                total_duration += assignment.get_duration_in_days(start_date, end_date)
        per_sub = profit / len(self.plan_assignments)
        duration_per_sub = total_duration / len(self.plan_assignments) / 30.0
        duration_context = 'months' if duration_per_sub != 1 else 'month'
        return [
            self._create_metric_value(value=per_sub, is_primary=True, period=period, subperiod=period.main_subperiod, context="profit" ),
            self._create_metric_value(value=duration_per_sub, is_primary=False, period=period, subperiod=period.main_subperiod, number_format='months', context=duration_context)
        ]

    def _get_description_for_metric_value(self, value, period):
        return 'Lifetime value (LTV) is the average number of months a subscriber stays on your network and the profit made over this time.'


class RunRateMetric(PlanAssignmentMetric):
    name = 'Run-rate'
    key = 'run-rate'
    number_format = 'currency'

    def _get_profit(self, subperiod):
        start_date, end_date = subperiod.dates
        profit = 0
        for assignment in self.plan_assignments:
            if self._assignment_date_is_relevant(assignment, start_date, end_date):
                profit += assignment.get_prorated_client_profit(start_date, end_date)
        return profit

    def _calculate_growth(self, dates_of_interest, prior_dates):
        old_profit = self._get_profit(prior_dates)
        now_profit = self._get_profit(dates_of_interest)
        if old_profit == 0:
            return None
        return (now_profit - old_profit) / old_profit * 100

    def _calculate_simple_metric_values(self, period):
        growth = self._calculate_growth(period.main_subperiod, period.previous_subperiod)
        if growth is None:
            return [
                self._create_metric_value(value=None, is_primary=True, period=period, subperiod=period.main_subperiod)
            ]

        current_num_subscribers = SimPlanAssignment.objects.filter(plan__client=self.client, end_date__isnull=True).count()
        profit_per_subscriber = self._get_profit(period.previous_subperiod) / current_num_subscribers
        num_periods = period.future_extrapolation_periods
        end_num_subscribers = current_num_subscribers * ((1 + (growth / 100)) ** num_periods)
        run_rate = profit_per_subscriber * end_num_subscribers * num_periods

        return [
            self._create_metric_value(value=run_rate, is_primary=True, period=period, subperiod=period.main_subperiod)
        ]

    def get_extrapolation_display(self, period):
        return f'{period.future_extrapolation_periods} {period.periodicity_name}s'

    def _get_context_for_period(self, period, subperiod, extrapolated):
        return f'next {self.get_extrapolation_display(period)}'

    def _get_description_for_metric_value(self, value, period):
        if value is None:
            return 'Your run-rate calculation will be calculated once you have a full period of data.'
        return f'Your run-rate calculation is a forecast of the total profit generated over the next {self.get_extrapolation_display(period)} based on on your current growth and trajectory. '
