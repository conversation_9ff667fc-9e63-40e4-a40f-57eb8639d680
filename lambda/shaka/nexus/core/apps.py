from django.apps import AppConfig
from django.conf import settings


class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'

    def ready(self):
        import core.signals  # pylint: disable=unused-import,import-outside-toplevel
        if settings.DEBUG and settings.RUN_DEV_THREAD_RECACHE:
            from core.front_cache import dev_thread_recache  # pylint: disable=import-outside-toplevel
            dev_thread_recache()
