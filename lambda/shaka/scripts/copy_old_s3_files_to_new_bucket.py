import gzip
import sys
from datetime import datetime
from io import BytesIO

import boto3


def gzip_file_content(content):
    with BytesIO() as compressed:
        with gzip.GzipFile(fileobj=compressed, mode='wb') as f:
            f.write(content)
        return compressed.getvalue()


def source_key_to_dest_key(key):
    without_extension = key.split('.')[0]
    _, data_type, __, _, _, _, _, date_portion = without_extension.split('_')
    date = datetime.strptime(date_portion, '%Y%m%d%H%M%S')
    return f'{data_type.lower()}/{date.year}/{date.month:02}/{date.day:02}/{key}'


def copy_files_between_buckets(source_bucket, destination_bucket, prefix_fn):
    s3 = boto3.resource('s3')

    source_bucket = s3.Bucket(source_bucket)
    destination_bucket = s3.Bucket(destination_bucket)

    for obj in source_bucket.objects.all():
        file_key = obj.key
        if prefix_fn(file_key):
            destination_key = source_key_to_dest_key(file_key)
            print(f'Copying {file_key} to {destination_key}')
            sys.stdout.flush()

            file_content = obj.get()['Body'].read()

            # Gzip the content
            gzipped_content = gzip_file_content(file_content)


            # Upload the gzipped content to the destination bucket
            destination_bucket.put_object(Key=destination_key, Body=gzipped_content)

if __name__ == "__main__":
    copy_files_between_buckets('shaka-sftp-imports-data', 'prod-transatel-cdr-files', lambda key: '20240118' in key)
