export function getStepFromPath<TRoutes extends Record<string, string>>(
  stepRoutes: TRoutes,
  pathname: string | null
): keyof TRoutes | undefined {
  if (pathname === null || pathname === undefined) return;

  if (pathname === '' || pathname === '/') {
    for (const [step, route] of Object.entries(stepRoutes)) {
      if (route === '') {
        return step as keyof TRoutes;
      }
    }
    return undefined;
  }

  const pathSegments = pathname.split('/').filter(Boolean);
  const lastSegment = pathSegments[pathSegments.length - 1] || '';

  for (const [step, route] of Object.entries(stepRoutes)) {
    if (route === lastSegment) {
      return step as keyof TRoutes;
    }
  }

  return undefined;
}

export const createStepRoutes = (steps: readonly string[]) => {
  return steps.reduce<Record<string, string>>((acc, step) => {
    acc[step] = step;
    return acc;
  }, {});
};

export type ValidationErrorObject = {
  _errors?: string[];
  [key: string]: any;
};

export const flattenValidationErrors = (
  errors: ValidationErrorObject,
  parentKey?: string
): string[] => {
  const result: string[] = [];

  if (errors._errors) {
    const hasErrors =
      Array.isArray(errors._errors) && errors._errors.length > 0;
    if (hasErrors) {
      const prefix = parentKey ? `${parentKey}: ` : '';
      result.push(...errors._errors.map((msg) => `${prefix}${msg}`));
    }
  }

  for (const key of Object.keys(errors)) {
    if (key === '_errors') continue;
    const value = errors[key];
    if (typeof value === 'object' && value !== null) {
      result.push(...flattenValidationErrors(value, key));
    }
  }

  return result;
};

// export const isUKPublicHoliday = (date: Date): boolean => {
//   const dateString = date.toISOString().split('T')[0];
//   return UK_PUBLIC_HOLIDAYS.includes(dateString);
// };

// export const isLeapYear = (year) => {
//   const fullYear = year < 100 ? 2000 + parseInt(year) : parseInt(year);
//   return (fullYear % 4 === 0 && fullYear % 100 !== 0) || fullYear % 400 === 0;
// };

// account for stac code as well !!
export function formatPortingCode(code: string): string {
  if (!code) return '';
  return code.replace(/\s+/g, '').toUpperCase();
}

export const checkIsSafari = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return (
    /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS/.test(userAgent)
  );
};

export const checkIsAndroid = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return /Android/.test(userAgent);
};

export const checkIsIos = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent);
};

export const checkIsDesktop = () => {
  if (typeof window === 'undefined') return false;
  const userAgent = window.navigator.userAgent;
  return !/Mobile/.test(userAgent);
};

export const standariseNetworkError = (error: Error) => {
  // enhance !
  return [`network: ${error.message}. Please try again later.`];
};

export const createTestId = (name: string) => {
  if (!name) return '';
  return name.toLowerCase().replace(/\s+/g, '-');
};

export function getNextWorkingDay(
  startDate: Date,
  holidayList: string[]
): Date {
  const candidateDate = new Date(startDate);

  while (true) {
    candidateDate.setDate(candidateDate.getDate() + 1);
    const isoDate = candidateDate.toISOString().slice(0, 10);
    const isWeekend =
      candidateDate.getDay() === 0 || candidateDate.getDay() === 6;
    const isHoliday = holidayList.includes(isoDate);

    if (!isWeekend && !isHoliday) {
      break;
    }
  }
  return candidateDate;
}

export function getAvailableDatesRange(
  startDate: Date,
  numberOfDays: number,
  holidayList: string[] = []
): Date[] {
  const availableDates: Date[] = [];
  const candidateDate = new Date(startDate);
  const maxDate = new Date(startDate);
  maxDate.setDate(startDate.getDate() + 30);

  while (true) {
    const hasReachedDateLimit = candidateDate >= maxDate;
    const hasReachedWorkingDayLimit = availableDates.length >= numberOfDays;

    if (hasReachedDateLimit || hasReachedWorkingDayLimit) {
      break;
    }

    candidateDate.setDate(candidateDate.getDate() + 1);
    const isoDate = candidateDate.toISOString().slice(0, 10);
    const isWeekend =
      candidateDate.getDay() === 0 || candidateDate.getDay() === 6;
    const isHoliday = holidayList.includes(isoDate);

    if (!isWeekend && !isHoliday) {
      availableDates.push(new Date(candidateDate));
    }
  }
  return availableDates;
}

// add tests
export function getRemainingDays(
  periodEnd: Date | string,
  currentDate: Date | string = new Date()
): number {
  const endDate = periodEnd instanceof Date ? periodEnd : new Date(periodEnd);

  const startDate =
    currentDate instanceof Date ? currentDate : new Date(currentDate);

  const MILLISECONDS_PER_DAY = 1000 * 60 * 60 * 24;

  const normalizedEndDate = new Date(
    endDate.getFullYear(),
    endDate.getMonth(),
    endDate.getDate()
  );
  const normalizedStartDate = new Date(
    startDate.getFullYear(),
    startDate.getMonth(),
    startDate.getDate()
  );

  const timeDiff = normalizedEndDate.getTime() - normalizedStartDate.getTime();
  return Math.ceil(timeDiff / MILLISECONDS_PER_DAY);
}

export function constructRemainingDaysMessage(
  remainingDays: number
): string | null {
  if (remainingDays === 0) return null;
  if (remainingDays === 1) return '1 day left';
  return `${remainingDays} days left`;
}
