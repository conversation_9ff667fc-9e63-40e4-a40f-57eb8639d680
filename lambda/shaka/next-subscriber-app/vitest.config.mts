import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    setupFiles: ['./setup.ts'],
    environment: 'jsdom',
    include: ['**/*.test.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: [
      'e2e/**/*.spec.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'node_modules',
      'dist',
      'build'
    ],
    globals: true
  }
});
