# In Next.js, .env.local is used for local environment variables.
# These variables are automatically loaded and available in your application.
# The NEXT_PUBLIC_ prefix makes a variable accessible on the client-side,
# while variables without this prefix are only available on the server-side.

# When creating a new client, create a new env file in this directory and add it to .gitignore.

NEXT_PUBLIC_CLIENT_ID=
NEXT_PUBLIC_CLIENT_NAME=
NEXT_PUBLIC_THEME_LOGO_URL=
NEXT_PUBLIC_THEME_FAVICON_URL=
NEXT_PUBLIC_FEATURES=
NEXT_PUBLIC_ANALYTICS_PROVIDER=
NEXT_PUBLIC_ANALYTICS_TRACKING_ID=
STRIPE_SECRET_KEY=