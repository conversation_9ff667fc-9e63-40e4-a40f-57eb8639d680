import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useState, useEffect, useRef, useCallback } from 'react';

interface PollingConfig<TData> {
  queryKey: string[];
  queryFn: () => Promise<TData>;
  expectedCount?: number;
  expectedCondition?: (data: TData) => boolean;
  pollingInterval?: number;
  maxAttempts?: number;
  enabled?: boolean;
  queryOptions?: Omit<
    UseQueryOptions<TData>,
    'queryKey' | 'queryFn' | 'refetchInterval'
  >;
}

interface PollingResult<TData> {
  data: TData | undefined;
  isPending: boolean;
  error: Error | null;
  stopPolling: () => void;
  attemptCount: number;
  isPolling: boolean;
}

export function usePolling<TData>({
  queryKey,
  queryFn,
  expectedCount,
  expectedCondition,
  pollingInterval = 2000,
  maxAttempts = 30,
  enabled = false,
  queryOptions = {}
}: PollingConfig<TData>): PollingResult<TData> {
  const [shouldPoll, setShouldPoll] = useState(enabled);
  const attemptCountRef = useRef(0);

  const stopPolling = useCallback(() => {
    setShouldPoll(false);
  }, []);

  useEffect(() => {
    setShouldPoll(enabled);
    if (enabled) {
      attemptCountRef.current = 0;
    }
  }, [enabled]);

  const wrappedQueryFn = async () => {
    attemptCountRef.current += 1;
    return queryFn();
  };

  const { data, isPending, error } = useQuery({
    queryKey,
    queryFn: wrappedQueryFn,
    refetchInterval: shouldPoll ? pollingInterval : false,
    refetchIntervalInBackground: true,
    enabled: enabled,
    ...queryOptions
  });

  const checkStopConditions = useCallback(
    (currentData: TData | undefined) => {
      if (!currentData) return false;

      // Handle the case where data is an object with esimData array
      const dataToCheck = (currentData as any)?.esimData || currentData;

      if (expectedCount && Array.isArray(dataToCheck)) {
        return dataToCheck.length >= expectedCount;
      }

      if (expectedCondition) {
        return expectedCondition(currentData);
      }

      return false;
    },
    [expectedCount, expectedCondition]
  );

  useEffect(() => {
    if (!shouldPoll || !data) return;

    const shouldStop =
      error ||
      checkStopConditions(data) ||
      attemptCountRef.current >= maxAttempts;

    if (shouldStop) {
      setShouldPoll(false);
    }
  }, [data, shouldPoll, checkStopConditions, maxAttempts, error]);

  return {
    data,
    isPending,
    error: error as Error | null,
    stopPolling,
    attemptCount: attemptCountRef.current,
    isPolling: shouldPoll
  };
}
