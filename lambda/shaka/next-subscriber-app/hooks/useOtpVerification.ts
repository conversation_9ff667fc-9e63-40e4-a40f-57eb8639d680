import { useState, useEffect, FormEvent } from 'react';

export interface UseOtpVerificationOptions {
  length?: number;
  validator?: (otp: string) => { success: boolean; error?: string };
  autoSubmit?: boolean;
  onComplete?: (otp: string) => void;
  resetOnError?: boolean;
}

export interface UseOtpVerificationReturn {
  otp: string;
  setOtp: (otp: string) => void;
  error: string | null;
  setError: (error: string | null) => void;
  isComplete: boolean;
  isValid: boolean;
  resetOtp: () => void;
  handleSubmit: (e: FormEvent) => void;
  isSubmitting: boolean;
}

export function useOtpVerification({
  length = 6,
  validator,
  autoSubmit = true,
  onComplete,
  resetOnError = false
}: UseOtpVerificationOptions = {}): UseOtpVerificationReturn {
  const [otp, setOtp] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const isComplete = otp.length === length;
  const isValid = isComplete && !error;

  const defaultValidator = (value: string) => {
    if (value.length !== length) {
      return {
        success: false,
        error: `Please enter all ${length} digits of the verification code`
      };
    }

    if (!/^\d+$/.test(value)) {
      return {
        success: false,
        error: 'The verification code must contain only numbers'
      };
    }

    return { success: true };
  };

  const validateOtp = (value: string) => {
    if (!value) {
      return { success: false, error: 'Please enter the verification code' };
    }

    return validator ? validator(value) : defaultValidator(value);
  };

  const resetOtp = () => {
    setOtp('');
    setError(null);
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    setError(null);

    const validation = validateOtp(otp);

    if (!validation.success) {
      setError(validation.error || 'Invalid verification code');
      if (resetOnError) {
        resetOtp();
      }
      return;
    }

    setIsSubmitting(true);

    if (onComplete) {
      try {
        // question. will it run api call first then redirect ?
        onComplete(otp);
      } catch (err) {
        console.error('Error in onComplete callback:', err);
        setError('An unexpected error occurred. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (autoSubmit && isComplete && !error) {
      const validation = validateOtp(otp);

      if (validation.success) {
        const form = document.querySelector('form');
        if (form) {
          form.dispatchEvent(
            new Event('submit', { cancelable: true, bubbles: true })
          );
        }
      } else {
        setError(validation.error || 'Invalid verification code');
      }
    }
  }, [otp, autoSubmit, isComplete, error]);

  useEffect(() => {
    if (otp) {
      if (isComplete) {
        const validation = validateOtp(otp);
        if (!validation.success) {
          setError(validation.error || 'Invalid verification code');
        } else {
          setError(null);
        }
      } else {
        setError(null);
      }
    }
  }, [otp, isComplete]);

  return {
    otp,
    setOtp,
    error,
    setError,
    isComplete,
    isValid,
    resetOtp,
    handleSubmit,
    isSubmitting
  };
}
