import { delay, http, HttpResponse } from 'msw';
import { AuthTokens } from '@/auth/tokens/token-service';

type LoginCredentials = {
  email: string;
  password: string;
};

export const mockSubscriptions = [
  {
    id: 1,
    subscriber: 1,
    current_plan: {
      id: 1,
      name: 'Unlimited',
      plan_details: {
        data_allowance_gb: 5,
        voice_allowance_minutes: 500,
        sms_allowance: 100,
        eu_roaming_enabled: false,
        row_roaming_enabled: false,
        eu_data_allowance_gb: 0,
        row_data_allowance_gb: 0,
        bundle_id: 'bundle_basic_01'
      },
      reference_id: null
    },
    usage: {
      europe: {
        data: {
          used: 1.2,
          remaining: 3.8
        },
        voice: {
          used: 120,
          remaining: 380
        },
        sms: {
          used: 20,
          remaining: 80
        }
      },
      uk: {
        data: {
          used: 1.2,
          remaining: 3.8
        },
        voice: {
          used: 120,
          remaining: 380
        },
        sms: {
          used: 20,
          remaining: 80
        }
      },
      period_start: '2025-07-01T00:00:00.000Z',
      period_end: '2025-07-31T23:59:59.000Z',
      billing_cycle_period: 'monthly'
    },
    roamingUsage: {
      daysUsed: 5,
      daysAllowed: 30
    },
    current_sim: {
      serial_number: '89012345678901234567',
      status: 'inactive',
      activation_date: null,
      current_msisdn: '447123456789',
      sim_type: 'esim',
      service_type: 'local',
      esim_data: {
        qr_code_base64: 'iVBORw0KGgoAAAANSUhEUgAAAAUA...',
        qr_code_image: 'https://example.com/qr-code.png',
        sm_dp_address: 'smdp.example.com',
        activation_code: 'ACT123456789',
        ios_universal_link: 'https://example.com/ios-link',
        android_activation_data: 'ACTDATA_ANDROID'
      }
    },
    current_msisdn: '447123456789',
    start_date: '2025-07-08T10:10:59.419Z',
    end_date: null,
    status: 'active',
    service_type: 'local',
    current_billing_cycle_end: '2025-07-31T23:59:59.000Z',
    next_billing_cycle_start: '2025-08-01T00:00:00.000Z',
    billing_cycle_period: 'monthly',
    reference_id: null
  }
];

const mockSimsData = [
  {
    serial_number: '89012345678901234567',
    status: 'inactive',
    activation_date: null,
    current_msisdn: '447123456789',
    sim_type: 'esim',
    service_type: 'local',
    esim_data: {
      qr_code_base64: 'base64encodedstring1',
      qr_code_image:
        'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png"',
      sm_dp_address: 'smdp.example.com',
      activation_code: 'LPA:1$smdp.example.com$MOCK-ACTIVATION-CODE-1',
      ios_universal_link: 'https://example.com/activate1',
      android_activation_data: 'androidactivationdata1'
    }
  },
  {
    serial_number: '89012345678901234561',
    status: 'inactive',
    activation_date: null,
    current_msisdn: '447123456781',
    sim_type: 'esim',
    service_type: 'local',
    esim_data: {
      qr_code_base64: 'base64encodedstring2',
      qr_code_image:
        'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png"',
      sm_dp_address: 'smdp.example.com',
      activation_code: 'LPA:1$smdp.example.com$MOCK-ACTIVATION-CODE-2',
      ios_universal_link: 'https://example.com/activate2',
      android_activation_data: 'androidactivationdata2'
    }
  },
  {
    serial_number: '89012345678901234562',
    status: 'inactive',
    activation_date: null,
    current_msisdn: '447123456782',
    sim_type: 'esim',
    service_type: 'local',
    esim_data: {
      qr_code_base64: 'base64encodedstring3',
      qr_code_image:
        'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png"',
      sm_dp_address: 'smdp.example.com',
      activation_code: 'LPA:1$smdp.example.com$MOCK-ACTIVATION-CODE-3',
      ios_universal_link: 'https://example.com/activate3',
      android_activation_data: 'androidactivationdata3'
    }
  }
];

const mockPurchaseDetails = [
  {
    id: 1,
    name: 'Unlimited',
    sim_details_id: 'sim_1234567890',
    allowances: {
      europe_data: 20
    },
    travel_addons: [
      {
        id: 5,
        name: '1GB',
        allowances: {
          data: 1
        }
      }
    ]
  },
  {
    id: 2,
    name: '40GB',
    sim_details_id: 'sim_12348986',
    allowances: {
      europe_data: 10
    },
    travel_addons: [
      {
        id: 5,
        name: '1GB',
        allowances: {
          data: 1
        }
      }
    ]
  }
];

type RefreshTokenRequest = Pick<AuthTokens, 'refreshToken'>;

let releasedItems = 0;
let nextReleaseTime = Date.now();

export const apiHandlers = [
  http.get(
    'http://localhost:8000/next/api/v1/1/data/subscriptions',
    async ({ request }) => {
      console.log('[MSW] Handler triggered for:', request.url);

      return HttpResponse.json(mockSubscriptions, { status: 200 });
    }
  ),

  http.get(
    'http://localhost:8000/next/api/v1/1/data/sims/',
    async ({ request }) => {
      console.log('[MSW] Handler triggered for:', request.url);

      const now = Date.now();

      // Check if it's time to release the next item
      if (now >= nextReleaseTime && releasedItems < mockSimsData.length) {
        releasedItems++;
        // Set next release time (3-5 seconds from now)
        nextReleaseTime = now + Math.random() * 2000 + 3000;
        console.log(
          `[MSW] Released item ${releasedItems}, next release in ${(nextReleaseTime - now) / 1000}s`
        );
      }

      const currentData = mockSimsData.slice(0, releasedItems);
      console.log(`[MSW] Returning ${currentData.length} items`);
      return HttpResponse.json(currentData, { status: 200 });
    }
  ),

  http.get(
    'http://localhost:8000/next/api/v1/1/data/purchase-details/',
    async () => {
      return HttpResponse.json(mockPurchaseDetails, { status: 200 });
    }
  ),

  // LOGIN
  http.post(/\/auth\/login$/, async ({ request }) => {
    const { email, password } = (await request.json()) as LoginCredentials;
    // Happy path
    if (email === '<EMAIL>' && password === 'password123') {
      await new Promise((res) => setTimeout(res, 30)); // Simulate network delay
      return HttpResponse.json(
        {
          tokens: {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            idToken: 'mock-id-token',
            expiresAt: Date.now() + 60 * 60 * 1000 // 1 hour
          }
        },
        { status: 200 }
      );
    }
    // Simulate network/server error for specific test email
    if (email === '<EMAIL>') {
      return HttpResponse.json(
        { message: 'Network error: please try again later' },
        { status: 500 }
      );
    }
    // Unhappy path: invalid credentials
    return HttpResponse.json(
      {
        message: 'Invalid credentials'
      },
      { status: 401 }
    );
  }),

  // REFRESH
  http.post('*/auth/refresh-token', async ({ request, params }) => {
    const { refreshToken } = (await request.json()) as RefreshTokenRequest;
    console.log(
      'MSW: /auth/refresh-token called with',
      refreshToken,
      'URL:',
      params
    );
    // Happy path
    if (refreshToken === 'mock-refresh-token') {
      return HttpResponse.json({
        tokens: {
          accessToken: 'new-mock-access-token',
          refreshToken: 'new-mock-refresh-token',
          idToken: 'new-mock-id-token',
          expiresAt: Date.now() + 60 * 60 * 1000
        }
      });
    }
    // Error path for invalid token
    console.log('MSW: /auth/refresh-token INVALID token:', refreshToken);
    return HttpResponse.json(
      { message: 'Invalid or expired refresh token' },
      { status: 401 }
    );
  }),

  // LOGOUT
  http.post(/\/auth\/logout$/, async ({ request }) => {
    const { refreshToken } = (await request.json()) as RefreshTokenRequest;
    // Happy path (any valid refresh token)
    if (refreshToken && refreshToken.startsWith('mock')) {
      return HttpResponse.json({ message: 'Logged out' }, { status: 200 });
    }
    // Unhappy path: missing/invalid token
    return HttpResponse.json(
      {
        message: 'Invalid refresh token'
      },
      { status: 400 }
    );
  }),

  // SIGNUP
  http.post(/\/auth\/sign-up$/, async ({ request }) => {
    const { email } = (await request.json()) as LoginCredentials;
    // Happy path: any email/password not already used
    if (email !== '<EMAIL>') {
      return HttpResponse.json(
        {
          tokens: {
            accessToken: 'signup-access-token',
            refreshToken: 'signup-refresh-token',
            idToken: 'signup-id-token',
            expiresAt: Date.now() + 60 * 60 * 1000
          }
        },
        { status: 200 }
      );
    }
    // Unhappy path: user already exists
    return HttpResponse.json(
      {
        message: 'User already exists'
      },
      { status: 409 }
    );
  }),

  http.post(
    'http://localhost:8000/next/api/v1/1/subscriptions/:email/share-esim/',
    async ({ params }) => {
      const { email } = params;

      await delay(1000);

      if (email) {
        return HttpResponse.json({ message: 'Email sent' }, { status: 200 });
      }

      return HttpResponse.json({ message: 'Missing email' }, { status: 400 });
    }
  ),

  // DEBUG PURPOSES
  http.post('*', async ({ params }) => {
    console.log('MSW: Unhandled POST request', params);
    return HttpResponse.json({ message: 'Unhandled' }, { status: 500 });
  })
];
