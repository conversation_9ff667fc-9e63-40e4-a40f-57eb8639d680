import { StripeMode } from '@/lib/stripe/types';
import { Email } from '@/src/uswitch/schemas/schemas';

export const API_ENDPOINTS = {
  dashboard: '/dashboard',
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh-token',
    signup: '/auth/sign-up',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password'
  },
  stripe: {
    createCheckoutSession: (
      planId: string,
      mode: StripeMode,
      returnUrl: string
    ) => `/plans/sign-up/${planId}/?ui_mode=${mode}&return_path=${returnUrl}/`,
    // correlation id ????
    checkoutSessionStatus: (sessionId: string) =>
      `/checkout/session/status/?session_id=${sessionId}`
  },
  wizard: {
    // will change most likely - more granular
    pacCode: (data: any) => `/subscription/pac-code/?pac_code=${data}`
  },
  subscriptions: {
    base: '/data/subscriptions',
    byId: (id: string) => `/subscriptions/${id}`,
    cancel: (id: string) => `/subscriptions/${id}/cancel`,
    pause: (id: string) => `/subscriptions/${id}/pause`,
    resume: (id: string) => `/subscriptions/${id}/resume`,
    shareEsim: (email: Email) => `/subscriptions/${email}/share-esim/`
  },
  plans: {
    base: '/plans',
    byId: (id: string) => `/plans/${id}`
  },
  sims: {
    base: '/data/sims/',
    purchaseDetails: '/data/purchase-details/'
  }
} as const;

export type ApiEndpoints = typeof API_ENDPOINTS;

// .get(`/checkout/session/status/?session_id=${sessionId}`)

// api.post(`/plans/sign-up/${planId}/`).then((res) => res.data);
