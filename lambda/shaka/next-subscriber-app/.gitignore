# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/
/dist/
/src/**/dist/
/src/**/.next/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
/src/uswitch/.env.local
/src/subscriber-app/.env.local
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
!/smtp.py
/client-config/subscriber.env
/client-config/uswitch.env

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/docs/addon-selection-algorithm.md
/docs/plan-addon-relationship-demo.md
/docs/plan-addon-relationship-diagram.md
/ADDON_DOMAIN_MODELS.md
/ADDON_FLOW_DOCUMENTATION.md
/ARCHITECTURE.md
/CODEBASE_KNOWLEDGE_DUMP.md
/REPOSITORY_OVERVIEW.md
/docs/METADATA_GUIDELINES.md
/CLAUDE.md
