import React from 'react';
import { UserProfileIcon } from '@/icons/icons';
import { useUswitchAuth } from '@/src/uswitch/app/signup/context/use-uswitch-auth';

export interface NavBarProps {
  logo: React.ReactNode;
  showUserProfile?: React.ReactNode;
  className?: string;
}

export function NavBar({ logo, showUserProfile, className }: NavBarProps) {
  return (
    <header className="bg-secondary w-full">
      <nav
        className={`lg-px-0 mx-auto flex max-w-[1262px] items-center justify-between px-6 py-2 shadow-sm lg:px-2 lg:shadow-none ${className || ''}`}
        aria-label="Main navigation"
      >
        <div className="flex items-center">{logo}</div>
        {showUserProfile && <UserProfile />}
      </nav>
    </header>
  );
}

function UserProfile() {
  const { isAuthenticated } = useUswitchAuth();

  return (
    <>
      {isAuthenticated ? (
        <button className="cursor-pointer" aria-label="User profile">
          <UserProfileIcon />
        </button>
      ) : (
        // or maybe redirect user to our sign in page ?
        <a href="https://www.uswitch.com/account/signin/" target="_blank">
          Sign in
        </a>
      )}
    </>
  );
}
