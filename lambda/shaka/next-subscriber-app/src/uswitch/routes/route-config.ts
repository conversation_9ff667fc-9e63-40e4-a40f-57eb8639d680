// EXAMPLE
export const ROUTES_CONFIG = {
  login: {
    path: '/login',
    name: 'Login'
  },
  signup: {
    path: '/signup',
    name: 'Signup'
  },
  'sign-in': {
    path: 'https://www.uswitch.com/account/signin/',
    name: 'Sign in'
  },
  'add-ons': {
    path: '/signup/add-ons',
    name: 'Add-ons'
  },
  'order-confirmation': {
    path: '/signup/payment/order-confirmation',
    name: 'Order Confirmation'
  },
  'payment-error': {
    path: '/signup/payment/failure',
    name: 'Error'
  },
  dashboard: {
    path: '/dashboard',
    name: 'Dashboard'
  },
  'dashboard-overview': {
    path: '/dashboard/overview',
    name: 'Overview'
  },
  'dashboard-track': {
    path: '/dashboard/track',
    name: 'Track'
  },
  'dashboard-esim': {
    path: '/dashboard/esim',
    name: 'eSIM'
  },
  'dashboard-manage': {
    path: '/dashboard/manage',
    name: 'Manage'
  },
  'dashboard-profile': {
    path: '/dashboard/user-profile',
    name: 'Profile'
  },
  'plan-selection': {
    path: '/signup/plan-selection',
    name: 'Explore Plans'
  },
  'number-porting': {
    path: '/signup/number-porting',
    name: 'Number Porting'
  },
  payment: {
    path: '/signup/payment',
    name: 'Payment'
  }
} as const;

export const signupRoutes = [
  ROUTES_CONFIG['plan-selection'].path,
  ROUTES_CONFIG['add-ons'].path,
  ROUTES_CONFIG['number-porting'].path,
  ROUTES_CONFIG['payment'].path,
  ROUTES_CONFIG['sign-in'].path
];

export type Route = (typeof ROUTES_CONFIG)[keyof typeof ROUTES_CONFIG]['path'];
