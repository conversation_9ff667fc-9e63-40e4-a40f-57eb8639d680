import { CreateSessionParams } from '@/lib/stripe/types';

export const stripeKeys = {
  all: ['stripe'] as const,
  sessions: () => [...stripeKeys.all, 'sessions'] as const,
  session: (params: CreateSessionParams) =>
    [...stripeKeys.sessions(), params] as const,
  sessionStatus: (sessionId: string) =>
    [...stripeKeys.sessions(), 'status', sessionId] as const
};

export const signupKeys = {
  planData: ['planData'] as const
};

export const subscriptionKeys = {
  subscriptions: ['subscriptions'] as const,
  subscription: (id: string) => [...subscriptionKeys.subscriptions, id] as const
};

export const simKeys = {
  esim: ['esim'],
  purchaseDetails: ['purchaseDetails'] as const
};
