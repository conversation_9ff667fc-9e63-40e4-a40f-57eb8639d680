import { test, expect, devices } from '@playwright/test';
import { ROUTES_CONFIG } from '../../routes/route-config';

const ROUTE = ROUTES_CONFIG['order-confirmation'].path;

const mockPurchaseDetails = [
  {
    id: 1,
    name: 'Unlimited Plan',
    price: 15.0,
    sim_details_id: 'sim_1234567890',
    allowances: {
      europe_data: 20
    }
  }
];

const mockEsimData = [
  {
    serial_number: '89012345678901234567',
    status: 'inactive',
    activation_date: null,
    current_msisdn: '447123456789',
    sim_type: 'esim',
    service_type: 'local',
    esim_data: {
      qr_code_base64: 'base64encodedstring1',
      qr_code_image: 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png',
      sm_dp_address: 'smdp.example.com',
      activation_code: 'LPA:1$smdp.example.com$MOCK-ACTIVATION-CODE-1',
      ios_universal_link: 'https://example.com/activate1',
      android_activation_data: 'androidactivationdata1'
    }
  }
];

const mockPlansData = [
  {
    id: 1,
    name: 'Unlimited Plan',
    allowances: {
      data: 'unlimited',
      calls: 'unlimited',
      texts: 'unlimited',
      europe_data: 20
    },
    travel_addons: [],
    price: 15.0,
    family_plans: []
  }
];

test.describe('Order Confirmation Responsive Design', () => {
  test.beforeEach(async ({ page }) => {
    await page.route('**/purchase-details', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockPurchaseDetails)
      });
    });

    await page.route('**/esims', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockEsimData)
      });
    });

    await page.route('**/plans', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockPlansData)
      });
    });
  });

  test('should show MobileOrderConfirmationFooter on mobile', async ({
    page
  }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show mobile footer
    await expect(
      page.getByTestId('mobile-order-confirmation-footer')
    ).toBeVisible({ timeout: 10000 });

    // Should show mobile-specific content
    await expect(
      page.getByText(/Video instructions|Help section/i)
    ).toBeVisible({ timeout: 10000 });
  });

  test('should hide mobile footer on desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should not show mobile footer
    await expect(
      page.locator(
        '[data-testid*="mobile-footer"], [data-testid*="order-confirmation-footer"]'
      )
    ).not.toBeVisible();
  });

  test('should handle orientation changes on mobile', async ({ page }) => {
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Test portrait orientation
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);

    await expect(page.locator('[data-testid*="qr-code"]')).toBeVisible({
      timeout: 10000
    });

    // Test landscape orientation
    await page.setViewportSize({ width: 667, height: 375 });
    await page.waitForTimeout(500);

    await expect(page.locator('[data-testid*="qr-code"]')).toBeVisible({
      timeout: 10000
    });
  });

  test('should maintain functionality across all breakpoints', async ({
    page
  }) => {
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    const breakpoints = [
      { width: 320, height: 568 }, // iPhone SE
      { width: 375, height: 667 }, // iPhone 6/7/8
      { width: 414, height: 896 }, // iPhone 11
      { width: 768, height: 1024 }, // iPad
      { width: 1024, height: 768 }, // Desktop small
      { width: 1440, height: 900 } // Desktop large
    ];

    for (const breakpoint of breakpoints) {
      await page.setViewportSize(breakpoint);
      await page.waitForTimeout(500);

      // Should show QR code or instructions at all breakpoints
      await expect(
        page.locator(
          '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
        )
      ).toBeVisible({ timeout: 10000 });
    }
  });

  test('should handle very small screens gracefully', async ({ page }) => {
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Test very small screen
    await page.setViewportSize({ width: 280, height: 480 });
    await page.waitForTimeout(500);

    // Should still show essential content
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });

    // Should not have horizontal scrolling
    const bodyWidth = await page.locator('body').boundingBox();
    expect(bodyWidth?.width).toBeLessThanOrEqual(280);
  });
});

test.describe('Order Confirmation Device-Specific Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.route('**/purchase-details', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockPurchaseDetails)
      });
    });

    await page.route('**/esims', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockEsimData)
      });
    });
  });

  test('should work correctly on iPhone SE', async ({ page }) => {
    await page.emulateMedia({ colorScheme: 'light' });
    await page.setViewportSize(devices['iPhone SE'].viewport);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show appropriate content for iPhone SE
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });

  test('should work correctly on iPhone 12', async ({ page }) => {
    await page.emulateMedia({ colorScheme: 'light' });
    await page.setViewportSize(devices['iPhone 12'].viewport);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show appropriate content for iPhone 12
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });

  test('should work correctly on iPad', async ({ page }) => {
    await page.emulateMedia({ colorScheme: 'light' });
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show tablet-optimized layout
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });

  test('should work correctly on Galaxy S9+', async ({ page }) => {
    await page.emulateMedia({ colorScheme: 'light' });
    await page.setViewportSize(devices['Galaxy S9+'].viewport);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show appropriate content for Galaxy S9+
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });

  test('should work correctly on Pixel 5', async ({ page }) => {
    await page.emulateMedia({ colorScheme: 'light' });
    await page.setViewportSize(devices['Pixel 5'].viewport);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show appropriate content for Pixel 5
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });
});

test.describe('Order Confirmation Accessibility', () => {
  test.beforeEach(async ({ page }) => {
    await page.route('**/purchase-details', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockPurchaseDetails)
      });
    });

    await page.route('**/esims', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockEsimData)
      });
    });
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should be able to tab through interactive elements
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');

    // Should maintain focus visibility - first check if there's a focused element
    const focusedElement = page.locator(':focus');
    const focusedElementCount = await focusedElement.count();

    if (focusedElementCount > 0) {
      await expect(focusedElement).toBeVisible();
    }
  });

  test('should support screen reader accessibility', async ({ page }) => {
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should have proper ARIA labels
    await expect(page.locator('[aria-label], [aria-labelledby]')).toHaveCount(
      1,
      { timeout: 10000 }
    );

    // Should have proper heading structure
    await expect(page.locator('h1, h2, h3')).toHaveCount(12, {
      timeout: 10000
    });
  });

  test('should support high contrast mode', async ({ page }) => {
    await page.emulateMedia({ colorScheme: 'dark' });
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show content in high contrast mode
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });

  test('should support reduced motion preferences', async ({ page }) => {
    await page.emulateMedia({ reducedMotion: 'reduce' });
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show content with reduced motion
    await expect(
      page.locator(
        '[data-testid*="qr-code"], [data-testid*="esim-instructions"]'
      )
    ).toBeVisible({ timeout: 10000 });
  });
});
