import { test, expect, devices } from '@playwright/test';
import { ROUTES_CONFIG } from '../../routes/route-config';

test.use({ ...devices['Pixel 5'] });

const ROUTE = ROUTES_CONFIG['order-confirmation'].path;

test.describe('eSIM Instructions - Android', () => {
  test('shows Android instructions on Android Chrome', async ({
    page,
    browserName
  }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    await page.goto(ROUTE);

    await expect(
      page.getByTestId('single-esim-instructions-android')
    ).toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-desktop"]')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-ios"]')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-ios-safari"]')
    ).not.toBeVisible();
  });
});
