import { test, expect } from '@playwright/test';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';

test.describe('PAC Porting Date Picker', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(ROUTES_CONFIG['number-porting'].path);
  });

  test('shows only valid working days in the date picker', async ({ page }) => {
    await expect(
      page.getByText('Choose your porting date (optional)')
    ).toBeVisible();

    const daySelect = page.getByLabel('Day');
    await expect(daySelect).toBeVisible();
    await daySelect.click();

    const dayOptions = await daySelect.locator('option').allTextContents();

    const forbiddenDays = ['Saturday', 'Sunday'];
    for (const forbidden of forbiddenDays) {
      expect(dayOptions).not.toContain(forbidden);
    }

    const validDayOptions = dayOptions.filter((opt) => opt !== 'DD');
    expect(validDayOptions.length).toBeLessThanOrEqual(30);
    expect(validDayOptions.length).toBeGreaterThan(0);
  });

  test('first available date is the next working day', async ({ page }) => {
    const daySelect = page.getByLabel('Day');
    await expect(daySelect).toBeVisible();
    await daySelect.click();
    const dayOptions = await daySelect.locator('option').allTextContents();

    const firstDay = dayOptions.find((opt) => opt !== 'DD');
    expect(Number(firstDay)).toBeGreaterThan(0);
  });

  test('date picker is accessible by label', async ({ page }) => {
    await expect(page.getByLabel('Day', { exact: true })).toBeVisible();
    await expect(page.getByLabel('Month', { exact: true })).toBeVisible();
    await expect(page.getByLabel('Year', { exact: true })).toBeVisible();
  });
});
