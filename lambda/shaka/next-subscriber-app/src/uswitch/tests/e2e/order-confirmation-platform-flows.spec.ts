import { test, expect, devices } from '@playwright/test';
import { ROUTES_CONFIG } from '../../routes/route-config';

const ROUTE = ROUTES_CONFIG['order-confirmation'].path;

const mockPurchaseDetails = [
  {
    id: 1,
    name: 'Unlimited Plan',
    price: 15.0,
    sim_details_id: 'sim_1234567890',
    allowances: {
      europe_data: 20
    }
  }
];

const mockEsimData = [
  {
    serial_number: '89012345678901234567',
    status: 'inactive',
    activation_date: null,
    current_msisdn: '447123456789',
    sim_type: 'esim',
    service_type: 'local',
    esim_data: {
      qr_code_base64: 'base64encodedstring1',
      qr_code_image: 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png',
      sm_dp_address: 'smdp.example.com',
      activation_code: 'LPA:1$smdp.example.com$MOCK-ACTIVATION-CODE-1',
      ios_universal_link: 'https://example.com/activate1',
      android_activation_data: 'androidactivationdata1'
    }
  }
];

const mockPlansData = [
  {
    id: 1,
    name: 'Unlimited Plan',
    allowances: {
      data: 'unlimited',
      calls: 'unlimited',
      texts: 'unlimited',
      europe_data: 20
    },
    travel_addons: [],
    price: 15.0,
    family_plans: []
  }
];

async function setupMockRoutes(page) {
  // Clear localStorage to ensure clean state
  await page.evaluate(() => {
    localStorage.clear();
  });

  await page.route('**/purchase-details', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockPurchaseDetails)
    });
  });

  await page.route('**/esims', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockEsimData)
    });
  });

  await page.route('**/plans', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockPlansData)
    });
  });
}

test.describe('eSIM Installation Flows - iOS Safari', () => {
  test('should show universal link installation for iOS Safari', async ({ browserName, browser }) => {
    test.skip(browserName !== 'webkit', 'Safari only');

    const context = await browser.newContext({
      ...devices['iPhone SE']
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show iOS Safari specific instructions
    await expect(page.getByTestId('single-esim-instructions-ios-safari')).toBeVisible({ timeout: 10000 });
    
    // Should not show other platform instructions
    await expect(page.getByTestId('single-esim-instructions-android')).not.toBeVisible();
    await expect(page.getByTestId('single-esim-instructions-ios')).not.toBeVisible();
    await expect(page.getByTestId('single-esim-instructions-desktop')).not.toBeVisible();

    await context.close();
  });

  test('should handle "Install eSIM" button interaction', async ({ browserName, browser }) => {
    test.skip(browserName !== 'webkit', 'Safari only');

    const context = await browser.newContext({
      ...devices['iPhone SE']
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show iOS Safari specific instructions first
    await expect(page.getByTestId('single-esim-instructions-ios-safari')).toBeVisible({ timeout: 10000 });
    
    // Should show Install eSIM button
    const installButton = page.getByRole('button', { name: /Install eSIM/i }).or(page.getByRole('link', { name: /Install eSIM/i }));
    await expect(installButton).toBeVisible({ timeout: 10000 });

    // Click should work (even if it opens system dialog)
    await installButton.click();
    
    // Button should remain functional
    await expect(installButton).toBeVisible();

    await context.close();
  });

  test('should show fallback if universal link fails', async ({ browserName, browser }) => {
    test.skip(browserName !== 'webkit', 'Safari only');

    const context = await browser.newContext({
      ...devices['iPhone SE']
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show both install button and QR code as fallback
    await expect(page.getByRole('button', { name: /Install eSIM/i }).or(page.getByRole('link', { name: /Install eSIM/i }))).toBeVisible({ timeout: 10000 });
    await expect(page.locator('[data-testid*="qr-code"]')).toBeVisible({ timeout: 10000 });

    await context.close();
  });
});

test.describe('eSIM Installation Flows - iOS Chrome', () => {
  test('should show QR code scanning instructions for iOS Chrome', async ({ browserName, browser }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    const ua = devices['iPhone SE'].userAgent.replace('Safari/', 'CriOS/');
    const context = await browser.newContext({
      ...devices['iPhone SE'],
      userAgent: ua
    });
    const page = await context.newPage();

    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show iOS Chrome specific instructions
    await expect(page.getByTestId('single-esim-instructions-ios')).toBeVisible({ timeout: 10000 });
    
    // Should not show other platform instructions
    await expect(page.getByTestId('single-esim-instructions-android')).not.toBeVisible();
    await expect(page.locator('[data-testid="single-esim-instructions-ios-safari"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="single-esim-instructions-desktop"]')).not.toBeVisible();

    await context.close();
  });

  test('should display step-by-step iOS installation guide', async ({ browserName, browser }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    const ua = devices['iPhone SE'].userAgent.replace('Safari/', 'CriOS/');
    const context = await browser.newContext({
      ...devices['iPhone SE'],
      userAgent: ua
    });
    const page = await context.newPage();

    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show step-by-step instructions
    await expect(page.getByText(/Settings/i)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/Cellular/i)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/Add eSIM/i)).toBeVisible({ timeout: 10000 });

    await context.close();
  });

  test('should handle QR code hard press instructions', async ({ browserName, browser }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    const ua = devices['iPhone SE'].userAgent.replace('Safari/', 'CriOS/');
    const context = await browser.newContext({
      ...devices['iPhone SE'],
      userAgent: ua
    });
    const page = await context.newPage();

    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show QR code with hard press instructions
    await expect(page.locator('[data-testid*="qr-code"]')).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/hard press|long press|3D Touch/i)).toBeVisible({ timeout: 10000 });

    await context.close();
  });
});

test.describe('eSIM Installation Flows - Android', () => {
  test('should show Android settings navigation', async ({ browserName, browser }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    const context = await browser.newContext({
      ...devices['Pixel 5']
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show Android specific instructions
    await expect(page.getByTestId('single-esim-instructions-android')).toBeVisible({ timeout: 10000 });
    
    // Should not show other platform instructions
    await expect(page.locator('[data-testid="single-esim-instructions-ios"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="single-esim-instructions-ios-safari"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="single-esim-instructions-desktop"]')).not.toBeVisible();

    await context.close();
  });

  test('should display Android-specific installation steps', async ({ browserName, browser }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    const context = await browser.newContext({
      ...devices['Pixel 5']
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show Android-specific terminology
    await expect(page.getByText(/Settings/i)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/Network|Connections/i)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/SIM|eSIM/i)).toBeVisible({ timeout: 10000 });

    await context.close();
  });

  test('should handle different Android versions', async ({ browserName, browser }) => {
    test.skip(browserName !== 'chromium', 'Chrome only');

    const context = await browser.newContext({
      ...devices['Pixel 5']
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show QR code for scanning
    await expect(page.locator('[data-testid*="qr-code"]')).toBeVisible({ timeout: 10000 });
    
    // Should provide alternative instructions for different Android versions
    await expect(page.getByText(/scan|QR code/i)).toBeVisible({ timeout: 10000 });

    await context.close();
  });
});

test.describe('eSIM Installation Flows - Desktop', () => {
  test('should show full desktop layout with video instructions', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show desktop specific instructions
    await expect(page.getByTestId('single-esim-instructions-desktop')).toBeVisible({ timeout: 10000 });
    
    // Should not show mobile instructions
    await expect(page.getByTestId('single-esim-instructions-android')).not.toBeVisible();
    await expect(page.locator('[data-testid="single-esim-instructions-ios"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="single-esim-instructions-ios-safari"]')).not.toBeVisible();
  });

  test('should display help section on desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show help section
    await expect(page.getByText(/Help|Support|FAQ/i)).toBeVisible({ timeout: 10000 });
    
    // Should show video instructions
    await expect(page.locator('video, iframe, [data-testid*="video"]')).toBeVisible({ timeout: 10000 });
  });

  test('should show QR code sharing options', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show QR code
    await expect(page.locator('[data-testid*="qr-code"]')).toBeVisible({ timeout: 10000 });
    
    // Should show sharing options
    await expect(page.getByRole('button', { name: /Share|Download|Save/i })).toBeVisible({ timeout: 10000 });
  });

  test('should show order summary in desktop layout', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show order summary
    await expect(page.locator('[data-testid*="order-summary"], [data-testid*="basket"]')).toBeVisible({ timeout: 10000 });
    
    // Should show plan details
    await expect(page.getByText(/Unlimited Plan/i)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/£15/i)).toBeVisible({ timeout: 10000 });
  });

  test('should handle desktop-specific interactions', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should handle right-click on QR code
    const qrCode = page.locator('[data-testid*="qr-code"]');
    await expect(qrCode).toBeVisible({ timeout: 10000 });
    
    // Right-click should work
    await qrCode.click({ button: 'right' });
    
    // Should handle keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
  });

  test('should show terms and conditions modal', async ({ page }) => {
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);

    // Should show terms link
    const termsLink = page.getByRole('link', { name: /Terms|Conditions/i }).or(page.getByRole('button', { name: /Terms/i }));
    await expect(termsLink).toBeVisible({ timeout: 10000 });
    
    // Click should open modal
    await termsLink.click();
    
    // Modal should be visible
    await expect(page.locator('[role="dialog"], [data-testid*="modal"]')).toBeVisible({ timeout: 5000 });
  });
});

test.describe('Cross-Platform and User Agent Detection', () => {
  test('should detect platform correctly across different devices', async ({ page }) => {
    // Test desktop detection
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);
    
    await expect(page.getByTestId('single-esim-instructions-desktop')).toBeVisible({ timeout: 10000 });
    
    // Test mobile detection
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    
    // Should show mobile-specific layout
    await expect(page.locator('[data-testid="single-esim-instructions-desktop"]')).not.toBeVisible();
  });

  test('should handle user agent switching', async ({ browser }) => {
    // Test with different user agents
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
    });
    const page = await context.newPage();
    
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);
    
    // Should show iOS Safari instructions
    await expect(page.getByTestId('single-esim-instructions-ios-safari')).toBeVisible({ timeout: 10000 });
    
    await context.close();
  });
  test('should mock and verify platform based on userAgent directly', async ({ browser }) => {
    // Mock Android user agent
    let context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Linux; Android 10; Pixel 3 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.92 Mobile Safari/537.36'
    });
    let page = await context.newPage();
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);
    await expect(page.getByTestId('single-esim-instructions-android')).toBeVisible({ timeout: 10000 });
    await context.close();

    // Mock iOS Safari user agent
    context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0 Safari/605.1.15'
    });
    page = await context.newPage();
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);
    await expect(page.getByTestId('single-esim-instructions-ios-safari')).toBeVisible({ timeout: 10000 });
    await context.close();

    // Mock Desktop user agent
    context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36'
    });
    page = await context.newPage();
    await setupMockRoutes(page);
    await page.goto(`${ROUTE}?session_id=test123&purchase_details_id=456`);
    await expect(page.getByTestId('single-esim-instructions-desktop')).toBeVisible({ timeout: 10000 });
    await context.close();
  });

});
