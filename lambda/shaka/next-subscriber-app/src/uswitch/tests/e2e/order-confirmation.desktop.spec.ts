import { test, expect } from '@playwright/test';
import { ROUTES_CONFIG } from '../../routes/route-config';

const ROUTE = ROUTES_CONFIG['order-confirmation'].path;

test.describe('eSIM Instructions', () => {
  test('shows Desktop instructions', async ({ page }) => {
    await page.goto(ROUTE);

    await expect(
      page.getByTestId('single-esim-instructions-android')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-ios"]')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-ios-safari"]')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-desktop"]')
    ).toBeVisible();
  });
});
