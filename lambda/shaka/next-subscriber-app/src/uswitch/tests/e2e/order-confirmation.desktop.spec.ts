import { test, expect } from '@playwright/test';
import { ROUTES_CONFIG } from '../../routes/route-config';

const ROUTE = ROUTES_CONFIG['order-confirmation'].path;

// Mock data
const mockPurchaseDetails = {
  purchase_details_id: 'test-purchase-123',
  session_id: 'test-session-456',
  status: 'completed'
};

const mockEsimData = [
  {
    esim_data: {
      qr_code_image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      ios_universal_link: 'https://esim.example.com/install',
      android_activation_data: {
        activation_code: 'test-activation-code',
        carrier_name: 'Test Carrier'
      }
    }
  }
];

const mockPlansData = [
  {
    id: 'test-plan-1',
    name: 'Test Plan',
    price: 10.00,
    data_allowance: '1GB'
  }
];

test.describe('eSIM Instructions', () => {
  test('shows Desktop instructions', async ({ page }) => {
    // Setup mock routes
    await page.route('**/purchase-details', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockPurchaseDetails)
      });
    });

    await page.route('**/esims', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockEsimData)
      });
    });

    await page.route('**/plans', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockPlansData)
      });
    });

    await page.goto(ROUTE);

    await expect(
      page.getByTestId('single-esim-instructions-android')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-ios"]')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-ios-safari"]')
    ).not.toBeVisible();

    await expect(
      page.locator('[data-testid="single-esim-instructions-desktop"]')
    ).toBeVisible();
  });
});
