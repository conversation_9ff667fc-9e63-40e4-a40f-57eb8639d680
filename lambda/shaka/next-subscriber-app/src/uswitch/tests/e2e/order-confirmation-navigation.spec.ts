import { test, expect } from '@playwright/test';
import { ROUTES_CONFIG } from '../../routes/route-config';

const ROUTE = ROUTES_CONFIG['order-confirmation'].path;

test.describe('Order Confirmation Navigation', () => {
  test('should redirect to test URL when purchase_details_id is missing', async ({
    page
  }) => {
    await page.goto(`${ROUTE}?session_id=test123`);

    // Should redirect to test URL with both parameters
    await expect(page).toHaveURL(/session_id=test123&purchase_details_id=456/);
  });

  test('should redirect to test URL when both parameters are missing', async ({
    page
  }) => {
    await page.goto(ROUTE);

    // Should redirect to test URL with default parameters
    await expect(page).toHaveURL(/session_id=test123&purchase_details_id=456/);
  });

  test('should handle invalid session_id parameter', async ({ page }) => {
    await page.route('**/purchase-details', async (route) => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Invalid session ID' })
      });
    });

    await page.goto(`${ROUTE}?session_id=invalid&purchase_details_id=456`);

    // Should show error for invalid session
    await expect(page.locator('[role="alert"]')).toBeVisible({
      timeout: 10000
    });
  });

  test('should handle malformed URL parameters', async ({ page }) => {
    await page.goto(
      `${ROUTE}?session_id=test%20123&purchase_details_id=456%20abc`
    );

    // Should handle malformed parameters gracefully
    await expect(page).toHaveURL(/session_id=.*&purchase_details_id=.*/);
  });

  test('should preserve URL parameters during redirects', async ({ page }) => {
    await page.goto(
      `${ROUTE}?session_id=preserve123&purchase_details_id=preserve456&extra=param`
    );

    // Should preserve the session_id and purchase_details_id
    await expect(page).toHaveURL(/session_id=preserve123/);
    await expect(page).toHaveURL(/purchase_details_id=preserve456/);
  });

  // test('should handle direct navigation to order confirmation', async ({ page }) => {
  //   // TODO: once auth is implemented this test should fail
  //   await page.route('**/purchase-details', async (route) => {
  //     await route.fulfill({
  //       status: 200,
  //       contentType: 'application/json',
  //       body: JSON.stringify([{ id: 1, name: 'Test Plan', price: 15.0 }])
  //     });
  //   });
  //
  //   await page.route('**/esims', async (route) => {
  //     await route.fulfill({
  //       status: 200,
  //       contentType: 'application/json',
  //       body: JSON.stringify([{ id: 'esim1', qrCode: 'test-qr', status: 'ready' }])
  //     });
  //   });
  //
  //   await page.goto(`${ROUTE}?session_id=direct123&purchase_details_id=direct456`);
  //
  //   // Should load successfully with direct navigation
  //   await expect(page.locator('[data-testid*="qr-code"], [data-testid*="esim-instructions"]')).toBeVisible({ timeout: 10000 });
  // });

  // test('should handle browser back/forward navigation', async ({ page }) => {
  //   // TODO: once auth is implemented this test should fail
  //   await page.route('**/purchase-details', async (route) => {
  //     await route.fulfill({
  //       status: 200,
  //       contentType: 'application/json',
  //       body: JSON.stringify([{ id: 1, name: 'Test Plan', price: 15.0 }])
  //     });
  //   });
  //
  //   await page.route('**/esims', async (route) => {
  //     await route.fulfill({
  //       status: 200,
  //       contentType: 'application/json',
  //       body: JSON.stringify([{ id: 'esim1', qrCode: 'test-qr', status: 'ready' }])
  //     });
  //   });
  //
  //   // Navigate to order confirmation
  //   await page.goto(`${ROUTE}?session_id=nav123&purchase_details_id=nav456`);
  //   await expect(page.locator('[data-testid*="qr-code"], [data-testid*="esim-instructions"]')).toBeVisible({ timeout: 10000 });
  //
  //   // Navigate away
  //   await page.goto('/');
  //   await expect(page).toHaveURL('/');
  //
  //   // Navigate back
  //   await page.goBack();
  //   await expect(page).toHaveURL(/order-confirmation/);
  //   await expect(page.locator('[data-testid*="qr-code"], [data-testid*="esim-instructions"]')).toBeVisible({ timeout: 10000 });
  //
  //   // Navigate forward
  //   await page.goForward();
  //   await expect(page).toHaveURL('/');
  // });

  test('should handle page refresh with valid parameters', async ({ page }) => {
    await page.route('**/purchase-details', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 1,
            name: 'Test Plan',
            price: 15.0,
            sim_details_id: 'sim_1234567890',
            allowances: {
              europe_data: 20
            }
          }
        ])
      });
    });

    await page.route('**/esims', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            serial_number: '89012345678901234567',
            status: 'inactive',
            activation_date: null,
            current_msisdn: '447123456789',
            sim_type: 'esim',
            service_type: 'local',
            esim_data: {
              qr_code_base64: 'base64encodedstring1',
              qr_code_image: 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png',
              sm_dp_address: 'smdp.example.com',
              activation_code: 'LPA:1$smdp.example.com$MOCK-ACTIVATION-CODE-1',
              ios_universal_link: 'https://example.com/activate1',
              android_activation_data: 'androidactivationdata1'
            }
          }
        ])
      });
    });

    await page.goto(
      `${ROUTE}?session_id=refresh123&purchase_details_id=refresh456`
    );
    await expect(
      page
        .locator('[data-testid*="qr-code"], [data-testid*="esim-instructions"]')
        .first()
    ).toBeVisible({ timeout: 10000 });

    // Refresh page
    await page.reload();
    await expect(page).toHaveURL(
      /session_id=refresh123&purchase_details_id=refresh456/
    );
    await expect(
      page
        .locator('[data-testid*="qr-code"], [data-testid*="esim-instructions"]')
        .first()
    ).toBeVisible({ timeout: 10000 });
  });
});
