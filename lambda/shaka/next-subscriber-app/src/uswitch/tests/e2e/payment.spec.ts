// import { test, expect } from '@playwright/test';
// import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
//
// const cardNumbers = {
//   valid: '****************',
//   genericDecline: '****************',
//   insufficientFunds: '****************',
//   lostCard: '****************',
//   stolenCard: '****************',
//   expiredCard: '****************'
// } as const;
//
// async function fillCardDetails(page: any, stripeTestCardNumber: string) {
//   const iframeLocator = page.frameLocator('div.__PrivateStripeElement iframe');
//   const cardNumber = iframeLocator.locator('#Field-numberInput');
//   await cardNumber.click();
//   await cardNumber.fill(stripeTestCardNumber);
//   const expDate = iframeLocator.locator('#Field-expiryInput');
//   await expDate.click();
//   await expDate.fill('12/34');
//   const cvc = iframeLocator.locator('#Field-cvcInput');
//   await cvc.click();
//   await cvc.fill('567');
//   const country = iframeLocator.locator('#Field-countryInput');
//   await country.selectOption('United Kingdom');
//   const zipCode = iframeLocator.locator('#Field-postalCodeInput');
//   await zipCode.click();
//   await zipCode.fill('NP19 1QG');
//
//   await page.getByRole('button', { name: /pay/i }).click();
// }
//
// test.describe('USwitch Payment Page', () => {
//   test.beforeEach(async ({ page }) => {
//     await page.goto(ROUTES_CONFIG.payment.path);
//   });
//
//   test('should show loading state while checkout session is being created', async ({
//     page
//   }) => {
//     const loadingMessage = page.getByText('Loading checkout...');
//     await expect(loadingMessage).toBeVisible();
//     await expect(loadingMessage).toBeHidden({ timeout: 30000 });
//     await expect(page.locator('form')).toBeVisible();
//   });
//
//   test('happy path: should process payment successfully with valid card details', async ({
//     page
//   }) => {
//     await fillCardDetails(page, cardNumbers.valid);
//     await page.waitForURL('**/payment/return', { timeout: 30000 });
//     await expect(page.url()).toContain('/payment/return');
//   });
//
//   test('unhappy path: should show error with invalid card details', async ({
//     page
//   }) => {
//     await fillCardDetails(page, cardNumbers.genericDecline);
//     await expect(page.getByRole('alert')).toBeVisible();
//   });
// });
