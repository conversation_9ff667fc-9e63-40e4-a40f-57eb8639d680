'use client';

import { getClientConfig } from '@/client-config/client-config';
import './globals.css';

export default function UswitchHomePage() {
  const config = getClientConfig();

  return (
    <main className="min-h-screen bg-gray-50 p-8">
      <h1 className="mb-8 text-center text-3xl font-bold">
        USwitch App Configuration
      </h1>
      <div className="mx-auto my-8 max-w-4xl rounded-lg bg-white p-6 shadow-md">
        <h2 className="mb-4 text-2xl font-bold">Client Configuration</h2>
        <pre className="max-h-[500px] overflow-auto rounded-lg bg-gray-100 p-4">
          {JSON.stringify(config, null, 2)}
        </pre>
      </div>
    </main>
  );
}
