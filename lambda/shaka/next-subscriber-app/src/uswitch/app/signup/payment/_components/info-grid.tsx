import { infoGrid } from '@/src/uswitch/utils/constants';
import React, { useState } from 'react';
import { QRCode } from '@/src/uswitch/app/signup/payment/_components/qrcode';
import Button from '@/src/uswitch/components/button/button';
import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import { EsimShare } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/esim-share';
import { QrCodeSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/qr-code-skeleton';
import { Addon } from '@/src/uswitch/schemas/schemas';

export function InfoGrid({ row = false }: { row?: boolean }) {
  if (row) {
    return (
      <ul className="text-xxs flex flex-row gap-2">
        {infoGrid.map((item) => (
          <InfoGridItem key={item.text} icon={item.icon} text={item.text} />
        ))}
      </ul>
    );
  }
  return (
    <ul className="text-xxs flex flex-col gap-4">
      {infoGrid.map((item) => (
        <InfoGridItem key={item.text} icon={item.icon} text={item.text} />
      ))}
    </ul>
  );
}

interface InfoGridItemProps {
  icon: React.ElementType;
  text: string;
}

export function InfoGridItem({ icon, text }: InfoGridItemProps) {
  const Icon = icon;
  return (
    <li className="flex items-center gap-4">
      <Icon className="flex-shrink-0" />
      <p className="font-bold opacity-100">{text}</p>
    </li>
  );
}

interface MultipleInfoGridItemProps {
  details: any;
  isPolling: boolean;
}

export function MultipleInfoGridItem({
  details,
  isPolling
}: MultipleInfoGridItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleShare = () => {
    setIsOpen(true);
  };

  if (!details) return;

  return (
    <div className="grid grid-cols-[1fr_1fr_auto] border-b border-gray-300 px-3 py-4">
      <div>
        <div className="flex flex-col gap-1">
          <div>
            <h3 className="leading-xs mr-1 inline text-sm">{details.name}</h3>
            <p className="text-default inline align-sub opacity-100">
              SIM plan
            </p>
          </div>
          <div className="flex flex-col gap-1">
            <p className="text-xxxs font-bold opacity-100">
              {details.allowances.europe_data}GB roaming in Europe
            </p>
            <p className="text-xxxs font-bold opacity-100">
              {details.travel_addons.map((addon: Addon) => (
                <span key={addon.id}>{addon.name} global roaming</span>
              ))}
            </p>
          </div>
        </div>
      </div>
      {isPolling ? (
        <QrCodeSkeleton size="small" />
      ) : (
        <QRCode qrcode="qrcode" size="small" />
      )}

      {isPolling ? (
        <div className="mt-8 h-9 w-36 animate-pulse rounded bg-gray-200" />
      ) : (
        <Button
          onClick={handleShare}
          className="self-center px-9"
          variant="secondary"
        >
          Share
        </Button>
      )}

      {isOpen && (
        <Modal onOpenChange={setIsOpen} open={isOpen}>
          <Modal.Overlay />
          <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
            <div className="mb-4 flex justify-end">
              <Modal.Close>
                <CloseIcon />
              </Modal.Close>
            </div>
            <Modal.Title className="mb-2 text-xs font-semibold">
              Share your eSIM
            </Modal.Title>
            <Modal.Description>
              <EsimShare />
            </Modal.Description>
          </Modal.Content>
        </Modal>
      )}
    </div>
  );
}
