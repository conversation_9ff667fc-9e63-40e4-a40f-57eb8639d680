import { ConditionalWrapper } from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { VideoInstructions } from '@/src/uswitch/app/signup/payment/_components/video-instructions';
import { HelpSection } from '@/src/uswitch/app/signup/payment/_components/help-section';
import { AfterPaymentOrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import React from 'react';
import { OrderConfirmationBanner } from './order-confirmation-banner';
import { MultipleInfoGridCore } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/multiple-info-grid-core';
import {
  InstructionStepsAndroid,
  InstructionStepsIos
} from '@/src/uswitch/app/signup/payment/order-confirmation/_components/instruction-steps-android';
import { TermsModalButton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/terms-modal-button';
import { AccountAccessSection } from '../../_components/account-access';
import { checkIsAndroid, checkIsIos, checkIsSafari } from '@/utils/helpers';
import { QRCode } from '@/src/uswitch/app/signup/payment/_components/qrcode';
import Button from '@/src/uswitch/components/button/button';
import { usePlanSelection } from '@/src/uswitch/app/signup/context/signup-context';
import { Alert } from '@/components/alert/alert';
import { QrCodeSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/qr-code-skeleton';

interface MultiPlanMobileProps {
  eSims: any;
  isPolling: boolean;
  error: Error | null;
}

export function MultiPlanMobile({
  eSims,
  isPolling,
  error
}: MultiPlanMobileProps) {
  const { orderSummary } = usePlanSelection();

  let MultiEsimInstruction: React.ComponentType<EsimInstructions>;

  if (checkIsIos() && checkIsSafari()) {
    MultiEsimInstruction = IphoneSafariMultiEsimInstructions;
  } else if (checkIsIos()) {
    MultiEsimInstruction = IphoneMultiEsimInstructions;
  } else if (checkIsAndroid()) {
    MultiEsimInstruction = AndroidMultipleEsimInstructions;
  } else {
    MultiEsimInstruction = AndroidMultipleEsimInstructions;
  }

  return (
    <ConditionalWrapper className="px-6 pt-6 lg:p-6">
      <OrderConfirmationBanner />
      <h2 data-testid="multiple-esim-instructions-mobile">Your eSIM QR code</h2>
      <p className="mb-5">
        We&apos;ll also email you your eSIM if you would like to do this later.
      </p>
      <div className="pb-4">
        <PlainCard className="mb-4 px-0 py-4">
          {error && (
            <Alert
              className="w-fit p-6"
              message={'We could not load plan details. Please try again'}
            />
          )}
          {MultiEsimInstruction &&
            eSims?.purchaseDetails?.map((details, index: number) => {
              const totalPlans = orderSummary.basketSummary.length;
              const isFirstItem = index === 0;
              const isLastItem = index === totalPlans - 1;
              const showTopMargin = index > 0;
              const showBottomBorder = !isLastItem;

              console.log(details, 'map');

              const containerClasses = [
                showTopMargin ? 'mt-2' : '',
                showBottomBorder ? 'border-b border-gray-300' : ''
              ]
                .filter(Boolean)
                .join(' ');

              return (
                <div key={`plan-${details.id}`} className="px-3 pb-4">
                  <MultipleInfoGridCore
                    isPolling={isPolling}
                    details={details}
                  />
                  <MultiEsimInstruction
                    isPolling={isPolling}
                    isFirstItem={isFirstItem}
                    containerClasses={containerClasses}
                  />
                </div>
              );
            })}
        </PlainCard>

        <Divider className="my-6" />
        <VideoInstructions />
        <Divider className="my-6" />
        <AccountAccessSection />
        <Divider className="my-6" />

        <h2 className="my-6">Need some help?</h2>
        <HelpSection />
        <Divider className="my-6" />
        <AfterPaymentOrderSummary
          basketSummary={orderSummary.basketSummary}
          cardLastThreeDigits={123}
          totalCost={orderSummary.totalCost}
        />
        <Divider />
        <div className="my-4">
          <p className="text-xxxs inline opacity-100">Full </p>
          <TermsModalButton />
          <p className="text-xxxs inline opacity-100"> apply.</p>
        </div>
      </div>
    </ConditionalWrapper>
  );
}

interface EsimInstructions {
  isFirstItem: boolean;
  isPolling: boolean;
  containerClasses: string;
}

function AndroidMultipleEsimInstructions({
  isFirstItem,
  containerClasses
}: EsimInstructions) {
  return (
    <div className={containerClasses}>
      {isFirstItem && (
        <>
          <Divider className="my-4" />
          <InstructionStepsAndroid />
        </>
      )}
    </div>
  );
}

function IphoneMultiEsimInstructions({
  isFirstItem,
  isPolling,
  containerClasses
}: EsimInstructions) {
  return (
    <div className={containerClasses}>
      {isFirstItem && (
        <>
          <Divider className="my-4" />
          <InstructionStepsIos />
          {isPolling ? (
            <QrCodeSkeleton
              className="mx-auto my-6 aspect-square h-full! max-h-[400px] w-full! max-w-[400px]"
              size="max"
            />
          ) : (
            <QRCode
              className="mx-auto my-6 aspect-square h-full! max-h-[400px] w-full! max-w-[400px]"
              qrcode="asd"
              size="max"
            />
          )}
        </>
      )}
    </div>
  );
}

function IphoneSafariMultiEsimInstructions({
  isFirstItem,
  isPolling,
  containerClasses
}: EsimInstructions) {
  // ideally a hook with logic to install it ?
  return (
    <div className={containerClasses}>
      {isFirstItem && (
        <>
          <Divider className="my-4" />
          {isPolling ? (
            <div className="mb-4 h-9 w-full animate-pulse rounded-lg bg-gray-200"></div>
          ) : (
            <Button variant="primary" className="mb-4 w-full">
              Install eSIM
            </Button>
          )}
        </>
      )}
    </div>
  );
}
