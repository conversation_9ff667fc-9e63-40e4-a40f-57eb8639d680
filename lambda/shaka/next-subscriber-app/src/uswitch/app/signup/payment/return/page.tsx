'use client';

import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect } from 'react';
import { useAuth } from '@/auth/hooks/use-auth';
import { useSuspenseQuery } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { LoadingSpinner } from '@/icons/icons';

// reference lambda/shaka/simp-webapp/src/app/(authenticated)/payment/return/page.tsx
// TEST FAILED PAYMENT

export default function PaymentReturnPage() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <PaymentReturn />
    </Suspense>
  );
}

function PaymentReturn() {
  const { apiClient } = useAuth();
  const searchParams = useSearchParams();
  const router = useRouter();
  const sessionId = searchParams.get('session_id');

  const { data: sessionStatus } = useSuspenseQuery({
    // replace with stipe query key
    queryKey: ['check-session-status', sessionId],
    queryFn: () =>
      paymentService.checkoutSessionStatus(apiClient, sessionId || ''),
    retry: false,
    staleTime: 1000
  });

  const { payment_status, status } = sessionStatus || {};
  const isSessionCompleted = status === 'complete';
  const isPaymentPaid = payment_status === 'paid';

  useEffect(() => {
    if (isSessionCompleted && isPaymentPaid) {
      router.replace(ROUTES_CONFIG['order-confirmation'].path);
    } else {
      router.replace(`${ROUTES_CONFIG['payment'].path}?error=payment_failed`);
    }
  }, [isPaymentPaid, isSessionCompleted, router]);

  // poll subscriber here ?

  return (
    <div
      className="flex h-screen w-full flex-col items-center justify-center"
      aria-live="polite"
      aria-atomic="true"
    >
      <div
        className="text-primary mb-4 size-9 animate-spin rounded-full border-3 border-current border-t-transparent"
        role="status"
        aria-hidden="true"
      ></div>
      <p
        className="text-[18px] opacity-100"
        id="transaction-status"
        aria-live="assertive"
      >
        Verifying card details
      </p>
      <div
        className="sr-only"
        role="status"
        aria-live="assertive"
        aria-busy={!isSessionCompleted}
      >
        Verifying card details
      </div>
    </div>
  );
}
