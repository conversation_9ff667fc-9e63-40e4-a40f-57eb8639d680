import {
  SkeletonBox,
  SkeletonCircle
} from '@/src/uswitch/app/signup/payment/order-confirmation/_components/desktop-order-confirmation-page-skeleton';
import { PlainCard } from '@/components/plain-card/plain-card';
import React from 'react';

const Row = () => (
  <div className="flex items-center justify-between gap-4">
    <div className="basis-[50%] space-y-3">
      <SkeletonBox className="h-5" />
      <SkeletonBox className="h-5" />
      <SkeletonBox className="h-5" width="w-3/4" />
    </div>
    <div className="h-9 w-24 animate-pulse rounded bg-gray-200" />
  </div>
);

export function OrderConfirmationMobileSkeleton() {
  return (
    <div className="mx-auto mt-4 max-w-sm bg-white p-2" data-testid="mobile-skeleton">
      {/* Title */}
      <SkeletonBox className="mb-6 h-6" />
      {/* Confirmation Message */}
      <div className="mb-6 rounded-xl border border-green-200 bg-green-50 p-4">
        <div className="flex items-start gap-3">
          <SkeletonCircle
            size="w-5 h-5 mt-1 flex-shrink-0"
            bgColor="bg-green-200"
          />
          <div className="flex-1 space-y-2">
            <SkeletonBox className="h-4" />
            <SkeletonBox className="h-4" />
            <SkeletonBox className="h-4" width="w-3/4" />
          </div>
        </div>
      </div>
      {/* eSIM QR Code Section */}
      <div>
        <SkeletonBox className="mb-3 h-7" />
        <div className="mb-6 space-y-2">
          <SkeletonBox className="h-4" />
          <SkeletonBox className="h-4" width="w-2/3" />
        </div>
      </div>
      <PlainCard className="space-y-8 p-6">
        <Row />
        <Row />
        <Row />
      </PlainCard>
    </div>
  );
}
