import Button from '@/src/uswitch/components/button/button';
import React, { useState } from 'react';
import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import { Addon } from '@/src/uswitch/schemas/schemas';
import { EsimShare } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/esim-share';

interface MultipleInfoGridCoreProps {
  details: any;
  isPolling: boolean;
}

export function MultipleInfoGridCore({
  details,
  isPolling
}: MultipleInfoGridCoreProps) {
  // hardcoded for now
  // logic for share button ? modal ?
  const [isOpen, setIsOpen] = useState(false);

  const handleShare = () => {
    setIsOpen(true);
    // post request
  };

  if (!details) return;

  return (
    <div className="grid grid-cols-[1fr_auto] items-center">
      <div className="flex flex-col">
        <div className="flex items-baseline">
          <h3 className="leading-xs mr-1 text-sm font-bold">{details.name}</h3>
          <p className="text-default text-sm opacity-100">SIM plan</p>
        </div>
        <div className="flex flex-col">
          <p className="text-xxxs font-bold opacity-100">
            {details.allowances.europe_data}GB roaming in Europe
          </p>
          <p className="text-xxxs font-bold opacity-100">
            {details.travel_addons.map((addon: Addon) => (
              <span key={addon.id}>{addon.name} global roaming</span>
            ))}
          </p>
        </div>
      </div>
      {isPolling ? (
        <div className="h-9 w-10 animate-pulse rounded-lg bg-gray-200"></div>
      ) : (
        <Button
          onClick={handleShare}
          className="ml-auto w-fit"
          variant="secondary"
        >
          Share
        </Button>
      )}
      {isOpen && (
        <Modal onOpenChange={setIsOpen} open={isOpen}>
          <Modal.Overlay />
          <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
            <div className="mb-4 flex justify-end">
              <Modal.Close>
                <CloseIcon />
              </Modal.Close>
            </div>
            <Modal.Title className="mb-6 text-xs font-semibold">
              Share your eSIM
            </Modal.Title>
            <Modal.Description>
              <EsimShare />
            </Modal.Description>
          </Modal.Content>
        </Modal>
      )}
    </div>
  );
}
