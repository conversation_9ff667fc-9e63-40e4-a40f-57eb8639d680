import { ConditionalWrapper } from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import {
  InfoGrid,
  MultipleInfoGridItem
} from '@/src/uswitch/app/signup/payment/_components/info-grid';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { VideoInstructions } from '@/src/uswitch/app/signup/payment/_components/video-instructions';
import { HelpSection } from '@/src/uswitch/app/signup/payment/_components/help-section';
import { AfterPaymentOrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import React from 'react';
import { TermsModalButton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/terms-modal-button';
import { AccountAccessSection } from '@/src/uswitch/app/signup/payment/_components/account-access';
import { usePlanSelection } from '@/src/uswitch/app/signup/context/signup-context';
import { BasketPlan } from '@/src/uswitch/app/signup/_reducer/reducer';
import { Alert } from '@/components/alert/alert';
import { InstructionBaseWrapper } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/instruction-base-wrapper';

interface MultiPlanDesktopProps {
  eSims: any;
  isPolling: boolean;
  error: Error | null;
}

export function MultiPlanDesktop({
  eSims,
  isPolling,
  error
}: MultiPlanDesktopProps) {
  const { orderSummary } = usePlanSelection();
  return (
    <>
      <ConditionalWrapper className="px-6 pt-6 lg:p-6">
        <InstructionBaseWrapper type="desktop-multiple">
          <InfoGrid row />
          <PlainCard className="mt-5 p-0">
            {error && (
              <Alert
                className="w-fit p-6"
                message={'We could not generate qr code. Please try again'}
              />
            )}
            {!error &&
              eSims?.purchaseDetails?.map((details: BasketPlan) => {
                return (
                  <MultipleInfoGridItem
                    key={details.id}
                    isPolling={isPolling}
                    details={details}
                  />
                );
              })}
          </PlainCard>
          <Divider className="my-6" />
          <VideoInstructions />
        </InstructionBaseWrapper>
      </ConditionalWrapper>

      <ConditionalWrapper className="px-6 pt-6 lg:p-6">
        <AccountAccessSection />
      </ConditionalWrapper>
      <ConditionalWrapper className="px-6 pt-6 lg:p-6">
        <h2 className="mb-6">Need some help?</h2>
        <HelpSection />
      </ConditionalWrapper>
      <ConditionalWrapper className="px-6 pt-6 lg:p-6">
        <AfterPaymentOrderSummary
          basketSummary={orderSummary.basketSummary}
          cardLastThreeDigits={123}
          totalCost={orderSummary.totalCost}
        />
        <Divider />
        <div className="my-4">
          <p className="text-xxxs inline opacity-100">Full </p>
          <TermsModalButton />
          <p className="text-xxxs inline opacity-100"> apply.</p>
        </div>
      </ConditionalWrapper>
    </>
  );
}
