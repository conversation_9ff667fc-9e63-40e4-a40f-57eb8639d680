import { PlainCard } from '@/components/plain-card/plain-card';
import { QrCodeSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/qr-code-skeleton';
import React from 'react';

export const SkeletonBox = ({ className = '', width = 'w-full' }) => (
  <div
    className={`rounded bg-gray-200 ${width} ${className} animate-pulse`}
  ></div>
);

export const SkeletonCircle = ({
  size = 'w-6 h-6',
  bgColor = 'bg-gray-200'
}) => <div className={`${bgColor} rounded-full ${size} animate-pulse`}></div>;

const Row = () => (
  <div className="grid grid-cols-[.6fr_1.6fr_.6fr] items-center gap-4">
    <div className="flex flex-col gap-2">
      <SkeletonBox className="h-5" />
      <SkeletonBox className="h-5" width="w-3/4" />
      <SkeletonBox className="h-5" width="w-3/4" />
    </div>
    <div>
      <QrCodeSkeleton className="mx-auto" size="small" />
    </div>
    <div className="mt-4 h-9 w-36 animate-pulse rounded bg-gray-200" />
  </div>
);

// Desktop Order Confirmation Skeleton
export function OrderConfirmationDesktopSkeleton() {
  return (
    <PlainCard className="mx-auto h-screen max-w-[827px] p-6" data-testid="desktop-skeleton">
      {/* Title */}
      <SkeletonBox className="mb-8 h-8" width="max-w-md" />

      {/* Confirmation Message */}
      <div className="mb-6 rounded-xl border-green-200 bg-green-50 p-6">
        <div className="flex items-start gap-4">
          <SkeletonCircle
            size="w-6 h-6 mt-1 flex-shrink-0"
            bgColor="bg-green-200"
          />
          <div className="flex-1 space-y-2">
            <SkeletonBox className="h-5" />
          </div>
        </div>
      </div>

      {/* eSIM QR Code Section */}
      <div className="mb-6">
        <SkeletonBox className="mb-4 h-4" width="max-w-xs" />
        <SkeletonBox className="mb-8 h-3" width="max-w-lg" />

        {/* Two Column Layout */}
        <div className="-mt-4 grid grid-cols-2 gap-8">
          {/* Left Column - Scan QR */}
          <div className="flex items-center gap-4">
            <div className="flex h-16 w-16 items-center justify-center rounded-xl bg-green-100"></div>
            <div className="flex-1 space-y-2">
              <SkeletonBox className="h-5" />
              <SkeletonBox className="h-5" width="w-2/3" />
            </div>
          </div>

          {/* Right Column - Add eSIM */}
          <div className="flex items-center gap-4">
            <div className="flex h-16 w-16 items-center justify-center rounded-xl bg-green-100"></div>
            <div className="flex-1 space-y-2">
              <SkeletonBox className="h-5" />
              <SkeletonBox className="h-5" width="w-3/4" />
            </div>
          </div>
        </div>
      </div>
      <PlainCard className="flex flex-col gap-4">
        <Row />
        <Row />
      </PlainCard>
    </PlainCard>
  );
}
