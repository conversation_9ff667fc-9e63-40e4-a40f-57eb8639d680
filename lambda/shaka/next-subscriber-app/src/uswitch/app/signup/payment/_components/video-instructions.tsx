import React from 'react';
import Button from '@/src/uswitch/components/button/button';
import Image from 'next/image';
import appleIcon from '@/public/images/apple-icon.png';
import androidIcon from '@/public/images/android-icon.png';
import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import { checkIsAndroid, checkIsDesktop, checkIsIos } from '@/utils/helpers';
import { useShowEsimInstructions } from '@/hooks/useShowEsimInstructions';
import {
  AndroidInstructionContent,
  AppleInstructionContent
} from '@/components/esim-instructions-content/esim-instructions-content';

export const phoneOperatingSystems = {
  android: 'android',
  iOS: 'apple'
} as const;

export type PhoneOS =
  (typeof phoneOperatingSystems)[keyof typeof phoneOperatingSystems];

export function VideoInstructions({ className = '' }: { className?: string }) {
  // temp
  const {
    isModalOpen,
    instructionType,
    handleModalClose,
    handleAppleClick,
    handleAndroidClick
  } = useShowEsimInstructions();

  let instructionButton: React.ReactNode = null;

  // MAY BE NEEDED in multip esim instruction
  // if (checkIsIos() && checkIsSafari()) {
  //   console.log('render checkIsIOS and Safari only');
  //   instructionButton = (
  //     <Button
  //       data-testid="single-esim-instructions-ios-safari"
  //       variant="primary"
  //       className="w-full"
  //     >
  //       Install eSIM
  //     </Button>
  //   );
  //   return instructionButton;
  // }

  if (checkIsIos()) {
    instructionButton = (
      <>
        <VideoInstructionsIntro />
        <Button
          className="mt-4 w-full sm:w-fit"
          variant="secondary"
          onClick={handleAppleClick}
        >
          <Image width={20} height={20} src={appleIcon} alt="Apple icon" />
          <span>iPhone instructions</span>
        </Button>
      </>
    );
  } else if (checkIsAndroid()) {
    instructionButton = (
      <>
        <VideoInstructionsIntro />
        <Button
          // data-testid="single-esim-instructions-android"
          className="mt-4 w-full sm:w-fit"
          variant="secondary"
          onClick={handleAndroidClick}
        >
          <Image width={23} height={23} src={androidIcon} alt="Android icon" />
          <span>Android instructions</span>
        </Button>
      </>
    );
  } else if (checkIsDesktop()) {
    instructionButton = (
      <>
        {/*<div className="grid grid-cols-1 items-end gap-x-4 gap-y-2 md:grid-cols-[.9fr_1.2fr]">*/}
        <VideoInstructionsIntro />
        <div className="flex flex-wrap items-end gap-2 md:flex-nowrap lg:ml-auto">
          <Button
            className="w-full sm:w-fit"
            variant="secondary"
            onClick={handleAppleClick}
          >
            <Image width={20} height={20} src={appleIcon} alt="Apple icon" />
            <span>iPhone instructions</span>
          </Button>
          <Button
            className="w-full sm:w-fit"
            variant="secondary"
            onClick={handleAndroidClick}
          >
            <Image
              width={23}
              height={23}
              src={androidIcon}
              alt="Android icon"
            />
            <span>Android instructions</span>
          </Button>
        </div>
      </>
    );
  }

  return (
    <>
      <div
        className={`grid grid-cols-1 gap-4 lg:grid-cols-[.7fr_1fr] ${className}`}
        data-testid="video-instructions"
      >
        {instructionButton}
      </div>
      {isModalOpen && (
        <Modal onOpenChange={handleModalClose} open={isModalOpen}>
          <Modal.Overlay />
          <Modal.Content className="w-full rounded-lg p-6 lg:max-w-4xl">
            <div className="mb-4 flex justify-end">
              <Modal.Close>
                <CloseIcon />
              </Modal.Close>
            </div>
            <Modal.Title className="mb-6 text-xs font-semibold lg:text-base">
              {instructionType === phoneOperatingSystems.iOS
                ? 'Installing your eSIM on an iPhone'
                : 'Installing your eSIM on an Android'}
            </Modal.Title>
            <Modal.Description>
              {instructionType === phoneOperatingSystems.iOS ? (
                <AppleInstructionContent />
              ) : (
                <AndroidInstructionContent />
              )}
            </Modal.Description>
          </Modal.Content>
        </Modal>
      )}
    </>
  );
}

function VideoInstructionsIntro() {
  return (
    <div>
      <h3 className="mb-2">Video instructions</h3>
      <p>
        Watch a brief instructional video to help you get your new eSIM
        installed
      </p>
    </div>
  );
}
