import React from 'react';
import Image from 'next/image';

interface QRCodeProps {
  qrcode: string;
  size?: 'small' | 'large' | 'max';
  className?: string;
}

const QR_CONFIG = {
  small: {
    dimensions: 128,
    containerStyle: {
      width: '128px',
      height: '128px'
    }
  },
  large: {
    dimensions: 227,
    containerStyle: {
      width: '227px',
      height: '227px'
    }
  },
  max: {
    dimensions: 0,
    containerStyle: {
      width: '100%',
      height: 'auto',
      aspectRatio: '1'
    }
  }
} as const;

export function QRCode({
  qrcode,
  className = '',
  size = 'large'
}: QRCodeProps) {
  const config = QR_CONFIG[size];
  const isMaxSize = size === 'max';

  console.log(qrcode);

  return (
    <div
      data-testid="qr-code"
      style={config.containerStyle}
      className={`border-success-border rounded-lg border-2 p-2 ${className}`}
    >
      <Image
        width={isMaxSize ? 500 : config.dimensions}
        height={isMaxSize ? 500 : config.dimensions}
        src={
          'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjgt9-FRGtHWBzbBHmvVjgfLGf6chckMLQUc0mgUGde0wsVHyd2vh00-6K1G5HrMgP9XU-UprhPxG7gDpMuTmcj8GZIbLHzcPnvf_1fPv9GB6Z-ObN227VJTkCpnVVARnf4V3TWmw3ApwA/s200/qr-code.png'
        }
        alt="QR Code"
        className={isMaxSize ? 'h-auto w-full' : ''}
        style={isMaxSize ? { width: '100%', height: 'auto' } : undefined}
      />
    </div>
  );
}
