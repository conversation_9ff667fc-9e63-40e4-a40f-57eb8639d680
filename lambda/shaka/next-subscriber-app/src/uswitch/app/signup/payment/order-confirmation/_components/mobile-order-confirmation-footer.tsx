import { usePlanSelection } from '@/src/uswitch/app/signup/context/signup-context';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { VideoInstructions } from '@/src/uswitch/app/signup/payment/_components/video-instructions';
import { AccountAccessSection } from '@/src/uswitch/app/signup/payment/_components/account-access';
import { HelpSection } from '@/src/uswitch/app/signup/payment/_components/help-section';
import { AfterPaymentOrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { TermsModalButton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/terms-modal-button';
import React from 'react';

export function MobileOrderConfirmationFooter() {
  const { orderSummary } = usePlanSelection();

  return (
    <div data-testid="mobile-order-confirmation-footer" className="px-3 pt-2">
      <Divider className="mb-4" />
      <VideoInstructions />
      <Divider className="mb-4" />
      <AccountAccessSection />
      <Divider className="mb-4" />
      <h2 className="mb-6">Need some help?</h2>
      <HelpSection />
      <Divider className="mb-4" />
      <AfterPaymentOrderSummary
        basketSummary={orderSummary.basketSummary}
        cardLastThreeDigits={123}
        totalCost={orderSummary.totalCost}
      />
      <Divider />
      <div className="my-4">
        <p className="text-xxxs inline opacity-100">Full </p>
        <TermsModalButton />
        <p className="text-xxxs inline opacity-100"> apply.</p>
      </div>
    </div>
  );
}
