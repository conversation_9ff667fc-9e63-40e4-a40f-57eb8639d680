import { usePlanSelection } from '@/src/uswitch/app/signup/context/signup-context';
import {
  checkIsAndroid,
  checkIsDesktop,
  checkIsIos,
  checkIsSafari
} from '@/utils/helpers';
import Button from '@/src/uswitch/components/button/button';
import {
  InstructionStepsAndroid,
  InstructionStepsIos
} from '@/src/uswitch/app/signup/payment/order-confirmation/_components/instruction-steps-android';
import { Alert } from '@/components/alert/alert';
import { QrCodeSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/qr-code-skeleton';
import { QRCode } from '@/src/uswitch/app/signup/payment/_components/qrcode';
import { ConditionalWrapper } from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { InfoGrid } from '@/src/uswitch/app/signup/payment/_components/info-grid';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { VideoInstructions } from '@/src/uswitch/app/signup/payment/_components/video-instructions';
import { AccountAccessSection } from '@/src/uswitch/app/signup/payment/_components/account-access';
import { HelpSection } from '@/src/uswitch/app/signup/payment/_components/help-section';
import { AfterPaymentOrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { TermsModalButton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/terms-modal-button';
import React from 'react';
import { InstructionBaseWrapper } from './instruction-base-wrapper';

interface SinglePlanSectionProps {
  eSims: any;
  isPolling: boolean;
  instructionError: Error | null;
}

export function SinglePlanSection({
  eSims,
  instructionError,
  isPolling
}: SinglePlanSectionProps) {
  const { orderSummary } = usePlanSelection();

  // consider passig those as props
  const { esim_data } = eSims?.esimData?.[0] || {};
  const purchaseDetails = eSims.purchaseDetails[0] || {};

  console.log(purchaseDetails);

  // in case of iphone we need ios_universal_link
  // andorid - android_activation_data

  if (checkIsIos() && checkIsSafari()) {
    // separate component with state to manage ios universal link ?? - check subs app
    return (
      <div className="px-3 pt-4">
        <InstructionBaseWrapper type="ios-safari">
          <Button
            onClick={() => console.log(esim_data.ios_universal_link)}
            className="w-full"
            variant="primary"
          >
            Install eSIM
          </Button>
        </InstructionBaseWrapper>
      </div>
    );
  } else if (checkIsIos()) {
    return (
      <div className="px-3 pt-4">
        <InstructionBaseWrapper type="ios">
          <InstructionStepsIos />
          {instructionError && (
            <Alert
              className="w-fit p-6"
              message={'We could not generate qr code. Please try again'}
            />
          )}
          {isPolling ? (
            <QrCodeSkeleton size="max" />
          ) : (
            <QRCode
              size="max"
              qrcode={esim_data.qr_code_image}
              className="mt-4"
            />
          )}
        </InstructionBaseWrapper>
      </div>
    );
  } else if (checkIsAndroid()) {
    // andoird activation_data here ? - check subs app
    return (
      <div className="px-3 pt-4">
        <InstructionBaseWrapper type="android">
          <InstructionStepsAndroid />
        </InstructionBaseWrapper>
      </div>
    );
  } else if (checkIsDesktop()) {
    return (
      <div className="mx-auto max-w-[827px]">
        <div className="flex flex-col gap-2">
          <ConditionalWrapper className="px-6 pt-6 lg:p-6">
            <InstructionBaseWrapper type="desktop">
              <div className="flex flex-wrap items-center gap-4 lg:gap-8">
                {instructionError && (
                  <Alert
                    message={'We could not generate qr code. Please try again'}
                  />
                )}
                {isPolling ? (
                  <QrCodeSkeleton size="large" />
                ) : (
                  <QRCode qrcode={esim_data.qr_code_image} size="large" />
                )}
                <InfoGrid />
              </div>
              <Divider className="my-6" />
              <VideoInstructions />
            </InstructionBaseWrapper>
          </ConditionalWrapper>
          <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
          <ConditionalWrapper className="px-6 lg:my-4 lg:p-6">
            <AccountAccessSection />
          </ConditionalWrapper>
          <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
          <ConditionalWrapper className="px-6 lg:my-4 lg:p-6">
            <h2 className="mb-6">Need some help?</h2>
            <HelpSection />
          </ConditionalWrapper>
          <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
          <ConditionalWrapper className="px-6 lg:p-6">
            <AfterPaymentOrderSummary
              basketSummary={orderSummary.basketSummary}
              cardLastThreeDigits={123}
              totalCost={orderSummary.totalCost}
            />
            <Divider />
            <div className="my-4">
              <p className="text-xxxs inline opacity-100">Full </p>
              <TermsModalButton />
              <p className="text-xxxs inline opacity-100"> apply.</p>
            </div>
          </ConditionalWrapper>
        </div>
      </div>
    );
  }
}
