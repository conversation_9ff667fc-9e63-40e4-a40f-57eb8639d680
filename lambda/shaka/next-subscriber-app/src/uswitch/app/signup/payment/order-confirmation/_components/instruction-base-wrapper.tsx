import React from 'react';
import { OrderConfirmationBanner } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/order-confirmation-banner';

// Export testid constants to avoid typos
export const INSTRUCTION_TEST_IDS = {
  ios: 'single-esim-instructions-ios',
  'ios-safari': 'single-esim-instructions-ios-safari',
  android: 'single-esim-instructions-android',
  desktop: 'single-esim-instructions-desktop',
  'desktop-multiple': 'multiple-esim-instructions-desktop'
} as const;

export type InstructionTestIdType = keyof typeof INSTRUCTION_TEST_IDS;

interface InstructionBaseWrapperProps extends React.PropsWithChildren {
  type: InstructionTestIdType;
}

export function InstructionBaseWrapper({
  type,
  children
}: InstructionBaseWrapperProps) {
  const dataTestId = INSTRUCTION_TEST_IDS[type];
  return (
    <div data-testid={dataTestId}>
      <OrderConfirmationBanner />
      <h2>Your eSIM QR code</h2>
      <p className="mb-5">
        We&apos;ll also email you your eSIM if you would like to do this later.
      </p>
      {children}
    </div>
  );
}
