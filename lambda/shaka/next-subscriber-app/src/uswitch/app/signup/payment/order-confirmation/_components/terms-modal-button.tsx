import { FullDetailsButton } from '@/src/uswitch/app/signup/_components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import React from 'react';

export function TermsModalButton() {
  return (
    <FullDetailsButton className="text-xxxs" text="terms and conditions">
      {({ isOpen, setIsOpen }) =>
        isOpen && (
          <Modal onOpenChange={setIsOpen} open={isOpen}>
            <Modal.Overlay />
            <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl" data-testid="terms-modal">
              <div className="mb-4 flex justify-end">
                <Modal.Close>
                  <CloseIcon />
                </Modal.Close>
              </div>
              <Modal.Title className="mb-6 text-xl font-semibold">
                hello
              </Modal.Title>
              <Modal.Description>hi</Modal.Description>
            </Modal.Content>
          </Modal>
        )
      }
    </FullDetailsButton>
  );
}
