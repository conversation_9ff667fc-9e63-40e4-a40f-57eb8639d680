import React from 'react';

interface QrCodeSkeletonProps {
  size?: 'small' | 'large' | 'max';
  className?: string;
}

const SKELETON_CONFIG = {
  small: {
    containerStyle: {
      width: '128px',
      height: '128px'
    }
  },
  large: {
    containerStyle: {
      width: '227px',
      height: '227px'
    }
  },
  max: {
    containerStyle: {
      width: '100%',
      height: 'auto',
      aspectRatio: '1'
    }
  }
} as const;

export function QrCodeSkeleton({
  size = 'large',
  className = ''
}: QrCodeSkeletonProps) {
  const config = SKELETON_CONFIG[size];
  const isMaxSize = size === 'max';

  return (
    <div
      data-testid="qr-code-skeleton"
      className={`animate-pulse rounded-lg border-2 border-gray-200 bg-gray-200 p-2  ${
        isMaxSize ? 'aspect-square w-full' : ''
      } mx-auto ${className}`}
      style={config.containerStyle}
    >
      <div className="flex h-full w-full items-center justify-center">
        <div className="h-16 w-16 animate-pulse rounded bg-gray-300" />
      </div>
    </div>
  );
}
