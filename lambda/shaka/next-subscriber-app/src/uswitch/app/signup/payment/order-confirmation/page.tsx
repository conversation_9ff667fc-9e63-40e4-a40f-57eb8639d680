'use client';

import React, { useEffect } from 'react';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { simService } from '@/services/simService';
import { useAuth } from '@/auth/hooks/use-auth';
import { simKeys } from '@/src/uswitch/query-keys/query-keys';
import { usePolling } from '@/hooks/usePolling';
import { Alert } from '@/components/alert/alert';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { OrderConfirmationDesktopSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/desktop-order-confirmation-page-skeleton';
import { OrderConfirmationMobileSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/mobile-order-confirmation-page-skeleton';
import { PlainCard } from '@/components/plain-card/plain-card';
import { SinglePlanSection } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/single-plan-section';
import { MobileOrderConfirmationFooter } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/mobile-order-confirmation-footer';
import { MultiPlanMobile } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/multi-plan-mobile';
import { MultiPlanDesktop } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/multi-plan-desktop';

export default function SuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const purchaseDetailsId = searchParams.get('purchase_details_id');
  // const sessionId = searchParams.get('session_id');
  const { apiClient } = useAuth();

  // imitate backend setting it up
  useEffect(() => {
    if (!purchaseDetailsId) {
      router.push(
        'order-confirmation/?session_id=test123&purchase_details_id=456'
      );
    }
  }, [purchaseDetailsId, router]);

  const {
    data: purchaseDetails,
    isPending: fetchingPurchaseDetails,
    error: purchaseDetailsError
  } = useQuery({
    queryKey: simKeys.purchaseDetails,
    queryFn: () => simService.getPurchaseDetails(apiClient),
    enabled: !!purchaseDetailsId
  });

  if (purchaseDetailsError) {
    return (
      <PlainCard className="mx-auto max-w-4xl">
        <Alert
          variant="error"
          message="We could not generate qr code. Please try again"
        />
      </PlainCard>
    );
  }

  return (
    // <RequireUswitchAuth>
    <QRCodeSection
      purchaseDetails={purchaseDetails}
      fetchingPurchaseDetails={fetchingPurchaseDetails}
    />
    // </RequireUswitchAuth>
  );
}

interface QRCodeSectionProps {
  // todo: add proper type once backend is ready
  purchaseDetails: any;
  fetchingPurchaseDetails: boolean;
}

function QRCodeSection({
  purchaseDetails,
  fetchingPurchaseDetails
}: QRCodeSectionProps) {
  const expectedEsimCount = purchaseDetails?.length;
  const { apiClient } = useAuth();
  const isMobileDevice = useMediaQuery(1024);

  const {
    data: eSims = [],
    error: instructionError,
    isPolling
    // todo: add proper type once backend is ready
  } = usePolling<any>({
    queryKey: simKeys.esim,
    queryFn: () => simService.getSims(apiClient),
    expectedCount: expectedEsimCount,
    pollingInterval: 2000,
    maxAttempts: 30,
    enabled: !!expectedEsimCount,
    queryOptions: {
      select: (data) => {
        return {
          purchaseDetails,
          esimData: data
        };
      }
    }
  });

  if (fetchingPurchaseDetails) {
    return isMobileDevice ? (
      <OrderConfirmationMobileSkeleton />
    ) : (
      <OrderConfirmationDesktopSkeleton />
    );
  }

  const hasMultiplePlans = expectedEsimCount > 1;

  const planSectionProps = {
    eSims: eSims,
    instructionError,
    isPolling
  };

  if (hasMultiplePlans) {
    return (
      <div className="mx-auto max-w-[827px]">
        <div className="flex flex-col gap-4">
          {isMobileDevice ? (
            <MultiPlanMobile
              eSims={eSims}
              error={instructionError}
              isPolling={isPolling}
            />
          ) : (
            <MultiPlanDesktop
              eSims={eSims}
              error={instructionError}
              isPolling={isPolling}
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <SinglePlanSection {...planSectionProps} />
      {isMobileDevice && <MobileOrderConfirmationFooter />}
    </>
  );
}
