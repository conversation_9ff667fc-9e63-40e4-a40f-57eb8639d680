// 'use client';
//
// import googleIcon from 'public/images/google-icon.png';
// import Image from 'next/image';
// import React from 'react';
// import { ConditionalWrapper } from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
// import Button from '@/src/uswitch/components/button/button';
// import { EnvelopeIcon } from '@/icons/icons';
// import { DeviderWithText } from '@/components/divider-with-text/divider-with-text';
// import {
//   SignInFormData,
//   useSignInFormSubmission
// } from '@/src/uswitch/app/signup/hooks/useSignInFormSubmission';
// import { getFieldError } from '@/src/uswitch/utils/helpers';
// import { Alert } from '@/components/alert/alert';
// import { useFocusError } from '@/hooks/useFocusError';
//
// export default function SignInSignUpPage() {
//   const { handleSubmit, isLoading, errors, formData } =
//     useSignInFormSubmission();
//   useFocusError(errors);
//
//   const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
//     e.preventDefault();
//
//     const form = e.currentTarget;
//     const formData = new FormData(form);
//
//     const data: SignInFormData = {
//       email: formData.get('email') as string
//     };
//
//     handleSubmit(data);
//   };
//
//   const handleGoogleAuth = () => {
//     console.log('Google auth');
//   };
//
//   const emailError = getFieldError('email', errors);
//
//   return (
//     <ConditionalWrapper className="relative mx-auto mt-[var(--spacing-6)] max-w-[608px] p-6">
//       <h1 className="mb-6 text-xs">Sign in or create account</h1>
//       <form onSubmit={onSubmit}>
//         <label className="font-semibold" htmlFor="email">
//           Email address
//         </label>
//         <div className="relative">
//           <input
//             type="email"
//             id="email"
//             name="email"
//             autoComplete="email"
//             className="border-border mt-2 w-full rounded-[2px] border p-3 pl-10"
//             defaultValue={formData.email}
//           />
//           <EnvelopeIcon className="absolute top-5 left-3" />
//         </div>
//         {emailError && (
//           <Alert
//             variant="error"
//             message={emailError}
//             align="left"
//             className="mt-2"
//           />
//         )}
//         <p className="my-3">We&#39;ll send you a code to sign in securely.</p>
//         <Button
//           variant="primary"
//           className="w-full"
//           isLoading={isLoading}
//           disabled={isLoading}
//           type="submit"
//         >
//           Continue
//         </Button>
//         <DeviderWithText className="my-5" text="or" />
//         <Button
//           onClick={handleGoogleAuth}
//           variant="secondary"
//           className="w-full"
//           type="button"
//           isLoading={isLoading}
//           disabled={isLoading}
//         >
//           <Image
//             src={googleIcon}
//             alt="Google icon"
//             width={14.4}
//             height={24}
//             className="mr-2"
//           />
//           <span>Sign in with Google</span>
//         </Button>
//       </form>
//     </ConditionalWrapper>
//   );
// }
