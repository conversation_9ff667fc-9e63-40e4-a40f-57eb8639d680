import React from 'react';

export function OrderSummarySkeleton() {
  return (
    <div className="hidden animate-pulse rounded-2xl bg-white p-4 shadow-lg lg:block">
      {/* Header */}
      <div className="mb-4 h-6 w-48 rounded-md bg-gray-300"></div>

      {/* Divider */}
      <div className="mb-6 h-px bg-gray-200"></div>

      {/* First item */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-5 w-6 rounded bg-gray-300"></div>
          <div className="h-5 w-20 rounded bg-gray-300"></div>
        </div>
        <div className="h-5 w-8 rounded bg-gray-300"></div>
      </div>

      {/* Second item */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-5 w-6 rounded bg-gray-300"></div>
          <div className="h-5 w-12 rounded bg-gray-300"></div>
        </div>
        <div className="h-5 w-8 rounded bg-gray-300"></div>
      </div>

      {/* Divider */}
      <div className="mb-6 h-px bg-gray-200"></div>

      {/* Total section */}
      <div className="flex items-end justify-between">
        <div className="h-6 w-20 rounded bg-gray-300"></div>
        <div className="text-right">
          <div className="mb-1 h-4 w-24 rounded bg-gray-300"></div>
          <div className="h-4 w-28 rounded bg-gray-300"></div>
        </div>
      </div>
    </div>
  );
}
