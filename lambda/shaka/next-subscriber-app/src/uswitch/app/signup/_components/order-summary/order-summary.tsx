import React, { Fragment, useState } from 'react';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { PlainCard } from '@/components/plain-card/plain-card';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { ChevronDown, ChevronUp } from '@/icons/icons';
import { OrderSummarySkeleton } from '@/src/uswitch/app/signup/_components/order-summary-skeleton/order-summary-skeleton';
import { BasketSummaryItem } from '@/src/uswitch/utils/helpers';

interface OrderSummaryProps {
  basketSummary: BasketSummaryItem[];
  totalCost: number;
  isPending?: boolean;
}

export function OrderSummary({
  basketSummary,
  totalCost,
  isPending
}: OrderSummaryProps) {
  const isMobileDevice = useMediaQuery(1024);

  return isMobileDevice ? (
    <OrderSummaryMobileWrapper
      isPending={isPending}
      basketSummary={basketSummary}
      totalCost={totalCost}
    />
  ) : (
    <OrderSummaryDesktopWrapper
      isPending={isPending}
      basketSummary={basketSummary}
      totalCost={totalCost}
    />
  );
}

interface OrderSummaryComponentProps {
  amount: number;
  basketSummary: BasketSummaryItem[];
}

export function OrderSummaryDesktop({
  amount,
  basketSummary
}: OrderSummaryComponentProps) {
  return (
    <div>
      <h3 id="order-summary-footer-heading">Order summary</h3>
      <Divider />
      <ol className="space-y-4" aria-labelledby="order-summary-footer-heading">
        {basketSummary?.map((item: BasketSummaryItem) => {
          const hasPlans = item.quantity > 0;
          const isPriceGreaterThanZero = item.planPrice + item.addonPrice > 0;
          const showItem = hasPlans && isPriceGreaterThanZero;

          return (
            <Fragment key={`${item.planId}_${item.addonId ?? 'null'}`}>
              {showItem && (
                <OrderSummaryRow
                  planName={item.planName}
                  addonName={item.addonName}
                  planPrice={item.planPrice}
                  addonPrice={item.addonPrice}
                  quantity={item.quantity}
                  hasAddon={item.hasAddon}
                />
              )}
            </Fragment>
          );
        })}
      </ol>
      <Divider />
      <TotalCostAside amount={amount} />
    </div>
  );
}

function OrderSummaryMobile({
  amount,
  basketSummary
}: OrderSummaryComponentProps) {
  const [toggleSummary, setToggleSummary] = useState(false);

  return (
    <section
      className="bg-blueberry-subtle p-4 px-6"
      aria-label="Basket summary"
    >
      <button
        aria-expanded={toggleSummary}
        aria-controls="order-summary-panel"
        onClick={() => setToggleSummary(!toggleSummary)}
        className="text-xxxs flex w-full items-center justify-between leading-6 font-bold"
      >
        Order summary
        {toggleSummary ? <ChevronDown /> : <ChevronUp />}
      </button>
      {toggleSummary && (
        <div
          id="order-summary-panel"
          aria-hidden={!toggleSummary}
          hidden={!toggleSummary}
        >
          <ol className="mt-6 space-y-4">
            {basketSummary.map((item) => {
              const showItem =
                item.quantity > 0 && item.planPrice + item.addonPrice > 0;
              return (
                <Fragment key={`${item.planId}_${item.addonId ?? 'null'}`}>
                  {showItem && (
                    <OrderSummaryRowMobile
                      planName={item.planName}
                      addonName={item.addonName}
                      planPrice={item.planPrice}
                      addonPrice={item.addonPrice}
                      quantity={item.quantity}
                      hasAddon={item.hasAddon}
                    />
                  )}
                </Fragment>
              );
            })}
          </ol>
          <Divider className="mb-6" />
          <TotalCostAside amount={amount} />
        </div>
      )}
    </section>
  );
}

interface OrderSummaryRowProps {
  planName: string;
  addonName: string | null;
  planPrice: number;
  addonPrice: number;
  quantity: number;
  hasAddon: boolean;
}

export function OrderSummaryRow({
  planName,
  addonName,
  planPrice,
  addonPrice,
  quantity,
  hasAddon
}: OrderSummaryRowProps) {
  const totalPrice = (planPrice + addonPrice) * quantity;

  return (
    <li className="flex items-start justify-between">
      <div className="flex items-start gap-2">
        <span className="sr-only">{`Quantity: ${quantity}`}</span>
        <span
          aria-hidden="true"
          className="bg-gray-subtle text-xxxs mt-0.5 rounded-xs px-[5px] py-[2px]"
        >
          {quantity}x
        </span>
        <div className="flex flex-col">
          <p className="text-xxxs font-medium">{planName} SIM plan</p>
          {hasAddon && addonName && (
            <p className="text-xxxs opacity-100">
              {addonName} monthly global roaming
            </p>
          )}
        </div>
      </div>
      <strong className="text-default">£{totalPrice}</strong>
    </li>
  );
}

function OrderSummaryRowMobile({
  planName,
  addonName,
  planPrice,
  addonPrice,
  quantity,
  hasAddon
}: OrderSummaryRowProps) {
  const totalPrice = (planPrice + addonPrice) * quantity;

  return (
    <li className="flex items-start justify-between">
      <div className="flex items-start gap-2">
        <span className="bg-secondary text-xxxs mt-0.5 rounded-xs px-[5px] py-[2px]">
          {quantity}x
        </span>
        <div className="flex flex-col">
          <p className="text-xxxs font-medium">{planName} SIM plan</p>
          {hasAddon && addonName && (
            <p className="text-xxxs opacity-100">
              {addonName} monthly global roaming
            </p>
          )}
        </div>
      </div>
      <strong className="text-default">£{totalPrice}</strong>
    </li>
  );
}

// Reuse the existing TotalCostAside component
interface TotalCostProps {
  amount: number;
}

export function TotalCostAside({ amount }: TotalCostProps) {
  return (
    <section
      aria-label={`Total cost £${amount} monthly, including VAT`}
      className="flex items-end justify-between"
    >
      <dl className="flex w-full flex-col items-end" aria-hidden="true">
        <div className="flex w-full justify-between">
          <dt className="text-default">Total cost</dt>
          <dd className="text-default">
            <strong data-testid="total-cost-aside">£{amount} monthly</strong>
            <span className="text-xxxs block text-right">(including VAT)</span>
          </dd>
        </div>
      </dl>
      <div
        aria-hidden="true"
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      >
        {`Total cost updated: £${amount} monthly, including VAT.`}
      </div>
    </section>
  );
}

function OrderSummaryMobileWrapper({ isPending, basketSummary, totalCost }) {
  if (isPending) {
    return <div className="mx-auto h-9 w-full animate-pulse bg-gray-300"></div>;
  }
  return (
    <OrderSummaryMobile basketSummary={basketSummary} amount={totalCost} />
  );
}

function OrderSummaryDesktopWrapper({ isPending, basketSummary, totalCost }) {
  if (isPending) {
    return <OrderSummarySkeleton />;
  }
  return (
    <PlainCard
      as="aside"
      ariaLabel="Order summary sidebar"
      className="bg-secondary top-1 order-1 max-w-[420px] min-w-[240px] self-start px-4 py-7 lg:order-2"
    >
      <OrderSummaryDesktop basketSummary={basketSummary} amount={totalCost} />
    </PlainCard>
  );
}

// Enhanced AfterPaymentOrderSummary component that supports addons
interface AfterPaymentOrderSummaryProps {
  basketSummary: BasketSummaryItem[];
  cardLastThreeDigits: number;
  totalCost?: number;
}

export function AfterPaymentOrderSummary({
  basketSummary,
  cardLastThreeDigits,
  totalCost
}: AfterPaymentOrderSummaryProps) {
  const calculatedTotal =
    totalCost || basketSummary?.reduce((sum, item) => sum + item.totalPrice, 0);

  return (
    <div data-testid="order-summary">
      <h3>Order summary</h3>
      <ol className="mt-6 space-y-4">
        {basketSummary?.map((item) => {
          const showItem =
            item.quantity > 0 && item.planPrice + item.addonPrice > 0;
          return (
            <Fragment key={`${item.planId}_${item.addonId ?? 'null'}`}>
              {showItem && (
                <OrderSummaryRow
                  planName={item.planName}
                  addonName={item.addonName}
                  planPrice={item.planPrice}
                  addonPrice={item.addonPrice}
                  quantity={item.quantity}
                  hasAddon={item.hasAddon}
                />
              )}
            </Fragment>
          );
        })}
      </ol>
      <br />
      <Divider className="mt-0" />
      <TotalCostAside amount={calculatedTotal} />
      <br />
      <footer
        aria-label="Payment details"
        className="flex items-end justify-between"
      >
        <div className="flex w-full flex-col items-end">
          <dl className="flex w-full items-end justify-between">
            <dt className="text-default">Payment details</dt>
            <dd className="text-default font-bold">
              <strong>Card ending</strong>
            </dd>
          </dl>
          <div className="text-xxxs text-right">
            **** **** **** {cardLastThreeDigits}
          </div>
        </div>
      </footer>
    </div>
  );
}
