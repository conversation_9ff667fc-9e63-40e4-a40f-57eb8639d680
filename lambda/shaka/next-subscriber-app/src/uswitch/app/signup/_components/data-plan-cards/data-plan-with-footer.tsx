import React from 'react';
import { OrderItem } from '@/src/uswitch/utils/constants';

interface DataPlanCardWithFooterProps {
  planDetails: OrderItem | undefined;
}

export function DataPlanCardWithFooter({
  planDetails
}: DataPlanCardWithFooterProps) {
  if (!planDetails) return;

  return (
    <div className="bg-blueberry-subtle border-blueberry-light order-2 ml-1 rounded-sm border text-center lg:col-start-2 lg:row-start-1">
      <div className="flex flex-wrap items-end justify-center gap-x-1 p-3">
        <h2 className="lg:text-sm">£{planDetails.price}</h2>
        <p className="text-xxxs lg:text-default lg:leading-7">a month</p>
      </div>
      <hr className="border-blueberry-light" />
      <div className="p-1">
        <p className="text-xxxs font-bold">30 day rolling contract</p>
      </div>
    </div>
  );
}
