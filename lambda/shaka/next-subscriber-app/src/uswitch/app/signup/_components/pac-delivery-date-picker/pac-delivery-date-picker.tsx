// import { getAvailableDatesRange } from '@/utils/helpers';
import React, { useEffect, useMemo, useState } from 'react';
import { monthNames } from '@/utils/constants';
import { ChevronDown } from '@/icons/icons';
import { Alert } from '@/components/alert/alert';
import { getAvailableDatesRange } from '@/utils/helpers';

interface DatePickerProps {
  initialState?: { day: string; month: string; year: string };
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  className?: string;
  holidays?: string[];
}

const baseSelectClasses = `
    w-full cursor-pointer appearance-none rounded-[2px] border p-2 xl:p-3
    bg-white text-placeholder rounded-[2px] border border-border
  `;

const iconClasses = `
    pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform
    transition-colors duration-200
  `;

export function PacDeliveryDatePicker({
  initialState = { day: '', month: '', year: '' },
  error = false,
  className = '',
  holidays = []
}: Omit<DatePickerProps, 'disabled' | 'required'>) {
  const today = new Date();
  const currentYear = today.getFullYear();

  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  // Only allow up to 30 working days in the next 30 calendar days
  const availableDates = getAvailableDatesRange(today, 30, holidays);

  const firstAvailableDate =
    availableDates.length > 0 ? availableDates[0] : tomorrow;

  const defaultDay = firstAvailableDate.getDate().toString().padStart(2, '0');
  const defaultMonth = (firstAvailableDate.getMonth() + 1)
    .toString()
    .padStart(2, '0');
  const defaultYear = firstAvailableDate.getFullYear().toString();

  const datesByMonth = useMemo(() => {
    const grouped: { [key: string]: Date[] } = {};

    availableDates.forEach((date) => {
      const monthKey = (date.getMonth() + 1).toString().padStart(2, '0');
      if (!grouped[monthKey]) {
        grouped[monthKey] = [];
      }
      grouped[monthKey].push(date);
    });

    return grouped;
  }, [availableDates]);

  // Check if dates span into next year
  const hasNextYearDates = useMemo(() => {
    return availableDates.some((date) => date.getFullYear() > currentYear);
  }, [availableDates, currentYear]);

  // Create array of available years
  const availableYears = useMemo(() => {
    const years = [currentYear.toString()];
    if (hasNextYearDates) {
      years.push((currentYear + 1).toString());
    }
    return years;
  }, [currentYear, hasNextYearDates]);

  const availableMonths = Object.keys(datesByMonth).map((monthValue) => ({
    name: monthNames[parseInt(monthValue) - 1],
    value: monthValue,
    disabled: false
  }));

  const [selectedDate, setSelectedDate] = useState({
    day: initialState.day || defaultDay,
    month: initialState.month || defaultMonth,
    year: initialState.year || defaultYear
  });

  const availableDays = useMemo(() => {
    if (!selectedDate.month || !datesByMonth[selectedDate.month]) {
      return [];
    }

    return datesByMonth[selectedDate.month]
      .map((date) => date.getDate().toString().padStart(2, '0'))
      .sort((a, b) => parseInt(a) - parseInt(b));
  }, [selectedDate.month, datesByMonth]);

  useEffect(() => {
    if (selectedDate.day && !availableDays.includes(selectedDate.day)) {
      setSelectedDate((prev) => ({
        ...prev,
        day: ''
      }));
    }
  }, [availableDays, selectedDate.day]);

  useEffect(() => {
    if (!selectedDate.month && defaultMonth) {
      setSelectedDate((prev) => ({
        ...prev,
        month: defaultMonth
      }));
    }
  }, [defaultMonth, selectedDate.month]);

  return (
    <div
      className={`mt-2 ${className}`}
      role="group"
      aria-labelledby="switchingDate"
    >
      <div className="grid grid-cols-3 gap-3">
        {/* Day */}
        <div className="relative">
          <label htmlFor="day-select" className="sr-only">
            Day
          </label>
          <select
            id="day-select"
            name="day"
            aria-label="Day"
            value={selectedDate.day}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, day: e.target.value })
            }
            disabled={!selectedDate.month || availableDays.length === 0}
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              DD
            </option>
            {availableDays.map((day) => (
              <option key={day} value={day}>
                {parseInt(day, 10)}
              </option>
            ))}
          </select>
          <span aria-hidden="true">
            <ChevronDown className={iconClasses} />
          </span>
        </div>

        {/* Month */}
        <div className="relative">
          <label htmlFor="month-select" className="sr-only">
            Month
          </label>
          <select
            id="month-select"
            name="month"
            aria-label="Month"
            value={selectedDate.month}
            onChange={(e) =>
              setSelectedDate({
                ...selectedDate,
                month: e.target.value,
                day: '' // Reset day when month changes
              })
            }
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
            disabled={availableMonths.length === 0}
          >
            <option value="" disabled>
              {availableMonths.length === 0 ? 'No dates available' : 'MM'}
            </option>
            {availableMonths.map((month) => (
              <option
                key={month.value}
                value={month.value}
                disabled={month.disabled}
              >
                {month.name}
              </option>
            ))}
          </select>
          <span aria-hidden="true">
            <ChevronDown className={iconClasses} />
          </span>
        </div>

        {/* Year */}
        <div className="relative">
          <label htmlFor="year-select" className="sr-only">
            Year
          </label>
          <select
            id="year-select"
            name="year"
            aria-label="Year"
            value={selectedDate.year}
            onChange={(e) =>
              setSelectedDate({
                ...selectedDate,
                year: e.target.value,
                month: '',
                day: ''
              })
            }
            className={baseSelectClasses}
          >
            {availableYears.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          <span aria-hidden="true">
            <ChevronDown className={iconClasses} />
          </span>
        </div>
      </div>

      {error && (
        <Alert
          message="Please select a valid date"
          variant="error"
          className="mt-1"
        />
      )}

      {availableDates.length === 0 && (
        <Alert message="No dates available" variant="error" className="mt-1" />
      )}
    </div>
  );
}
