import React from 'react';
import { QuantityInput } from '@/components/quantity-buttons/quantity-buttons';
import { FullDetailsButton } from '@/src/uswitch/app/signup/_components/full-details-button/full-details-button';
import { OrderItem, planOrderItems } from '@/src/uswitch/utils/constants';
import Modal from '@/components/modal/modal';
import { CloseIcon, TickIcon } from '@/icons/icons';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { createTestId } from '@/utils/helpers';
import { TermsConditions } from '@/src/uswitch/components/terms-conditions/terms-conditions';

interface PlanCardProps extends React.PropsWithChildren {
  planDetails: OrderItem;
  quantity: number;
  plans: React.ReactNode;
  handleIncrement: () => void;
  handleDecrement: () => void;
  minQuantity: number;
  maxQuantity?: number;
}

function getPlanDescriptionList(planDetailsId: number) {
  const selectedPlan = planOrderItems.find(
    (orderItem) => orderItem.id === planDetailsId
  );

  if (!selectedPlan) return null;

  return (
    <div key={selectedPlan.id} className="mb-4">
      <ol className="space-y-2">
        {Object.values(selectedPlan.description ?? {}).map((desc, index) => (
          <li className="flex items-center gap-2" key={index}>
            <TickIcon />
            <p className="text-primary-hover text-[18px] opacity-100">{desc}</p>
          </li>
        ))}
      </ol>
    </div>
  );
}

export function PlanCard({
  planDetails,
  quantity,
  plans,
  handleIncrement,
  handleDecrement,
  minQuantity,
  maxQuantity,
  children
}: PlanCardProps) {
  return (
    <div className="grid grid-cols-2 gap-y-3 px-2 pb-4 lg:grid-cols-[1fr_1fr_auto] lg:grid-rows-[1fr_auto] lg:gap-x-2 lg:gap-y-4 lg:px-3">
      {plans}
      <div className="order-4 col-span-3 flex flex-col items-center gap-6 self-start lg:order-3 lg:col-start-3 lg:row-start-1">
        <FullDetailsButton
          className="self-start lg:order-2 lg:self-center"
          text="Full details"
        >
          {({ isOpen, setIsOpen }) =>
            isOpen && (
              <Modal onOpenChange={setIsOpen} open={isOpen}>
                <Modal.Overlay />
                <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                  <div className="mb-4 flex justify-end">
                    <Modal.Close>
                      <CloseIcon />
                    </Modal.Close>
                  </div>
                  <Modal.Title className="mb-6 text-xl font-semibold">
                    <div className="flex items-end justify-between">
                      <h1 className="leading-none lg:text-base">
                        {planDetails.name} plan
                      </h1>
                      <span>
                        <strong className="text-xs leading-none lg:text-base">
                          £{planDetails.price}.00{' '}
                        </strong>
                        <span className="text-default text-xs leading-none font-normal lg:text-[18px]">
                          a month
                        </span>
                      </span>
                    </div>
                  </Modal.Title>
                  <Modal.Description>
                    <Divider />
                    <h2 className="mb-4">Your plan includes</h2>
                    {getPlanDescriptionList(planDetails.id)}
                    <br />
                    <TermsConditions />
                  </Modal.Description>
                </Modal.Content>
              </Modal>
            )
          }
        </FullDetailsButton>
        <QuantityInput.Root
          value={quantity}
          onIncrement={handleIncrement}
          onDecrement={handleDecrement}
          minQuantity={minQuantity}
          maxQuantity={maxQuantity}
        >
          <QuantityInput.Button
            data-testid={`decrement-${createTestId(planDetails.name)}`}
            action="decrement"
            aria-label={`Decrease quantity for ${planDetails.name}`}
          />
          <QuantityInput.Display
            data-testid={`${createTestId(planDetails.name)}-quantity`}
          />
          <QuantityInput.Button
            data-testid={`increment-${createTestId(planDetails.name)}`}
            action="increment"
            aria-label={`Increase quantity for ${planDetails.name}`}
          />
        </QuantityInput.Root>
      </div>
      {children}
    </div>
  );
}
