'use client';

import React from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import { OrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { usePathname } from 'next/navigation';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { NetworkInfo } from '@/src/uswitch/app/signup/_components/network-info/network-info';
import { PlanCard } from '@/src/uswitch/app/signup/_components/data-plan-cards/plan-card';
import { DataPlanDetails } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plans-details';
import { NextStepLink } from '@/src/uswitch/app/signup/_components/cta-button/next-step-link';
import {
  ConditionalWrapper,
  DesktopOnly
} from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { DataPlanCard } from '../_components/data-plan-cards/data-plan-card';
import { DataPlanCardWithFooter } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plan-with-footer';
import {
  createOrderSummary,
  shouldShowProgressBar
} from '@/src/uswitch/utils/helpers';
import {
  MAX_FAMILY_PLAN_QUANTITY,
  MAX_MAIN_PLAN_QUANTITY,
  MIN_FAMILY_PLAN_QUANTITY,
  MIN_MAIN_PLAN_QUANTITY,
  SignupState,
  BasketPlan
} from '@/src/uswitch/app/signup/_reducer/reducer';
import { usePlanSelection } from '@/src/uswitch/app/signup/context/signup-context';
import { TotalCostBottom } from '@/src/uswitch/app/signup/_components/total-cost-bottom/total-cost-bottom';
import { PlanCardSkeleton } from '@/src/uswitch/app/signup/_components/plan-card-skeleton/plan-card-skeleton';
import { Alert } from '@/components/alert/alert';
import useLocalStorage, { LocalKey } from '@/hooks/useLocalStorage';
import { MainPlan } from '@/src/uswitch/schemas/schemas';

interface PlanCardsSectionProps {
  planSelectionState: SignupState;
  basketPlans: BasketPlan[];
  handleBasicPlanIncrement: () => void;
  handleBasicPlanDecrement: () => void;
  handleFamilyPlanIncrement: () => void;
  handleFamilyPlanDecrement: () => void;
  planData: MainPlan[] | undefined;
  saveSignupStateToLocalStorage: (value: SignupState) => void;
}

function PlanCardsSection({
  planSelectionState,
  basketPlans,
  handleBasicPlanIncrement,
  handleBasicPlanDecrement,
  handleFamilyPlanIncrement,
  handleFamilyPlanDecrement,
  planData,
  saveSignupStateToLocalStorage
}: PlanCardsSectionProps) {
  const { mainPlanQuantity, familyPlanQuantity } = planSelectionState;
  return (
    <>
      {planData?.map((plan) => {
        // todo function
        const isAllUnlimited =
          plan.allowances.data === 'unlimited' &&
          plan.allowances.calls === 'unlimited' &&
          plan.allowances.texts === 'unlimited';
        return (
          <PlainCard key={plan.id} className="px-2">
            <NetworkInfo />
            <PlanCard
              planDetails={plan}
              plans={
                <>
                  <DataPlanCard planDetails={plan} />
                  <DataPlanCardWithFooter planDetails={plan} />
                </>
              }
              minQuantity={MIN_MAIN_PLAN_QUANTITY}
              maxQuantity={MAX_MAIN_PLAN_QUANTITY}
              quantity={mainPlanQuantity}
              handleIncrement={handleBasicPlanIncrement}
              handleDecrement={handleBasicPlanDecrement}
            >
              <DataPlanDetails>
                <h3 className="mb-2 text-[18px] lg:mb-0">
                  {plan.allowances.europe_data}GB in Europe
                </h3>
                <div className="border-border hidden border opacity-20 lg:block" />
                <h3 className="text-[18px]">
                  {isAllUnlimited ? 'Unlimited UK calls, texts and data' : ''}
                </h3>
              </DataPlanDetails>
            </PlanCard>
          </PlainCard>
        );
      })}

      <Divider className="my-8 mb-6" />
      <h2 className="my-2">Big family ?</h2>
      <p className="mb-6">
        Add up to 5 special rate plans for your family members
      </p>

      {planData?.flatMap((plan) =>
        plan.family_plans?.map((familyPlan) => {
          // function
          const isAllUnlimited =
            familyPlan.allowances.calls === 'unlimited' &&
            familyPlan.allowances.texts === 'unlimited';

          return (
            <PlainCard key={familyPlan.id} className="px-2">
              <NetworkInfo />
              <PlanCard
                planDetails={familyPlan}
                plans={
                  <>
                    <DataPlanCard planDetails={familyPlan} />
                    <DataPlanCardWithFooter planDetails={familyPlan} />
                  </>
                }
                minQuantity={MIN_FAMILY_PLAN_QUANTITY}
                maxQuantity={MAX_FAMILY_PLAN_QUANTITY}
                quantity={familyPlanQuantity}
                handleIncrement={handleFamilyPlanIncrement}
                handleDecrement={handleFamilyPlanDecrement}
              >
                <DataPlanDetails>
                  <h3 className="mb-2 text-[18px] lg:mb-0">
                    {familyPlan.allowances.europe_data}GB in Europe
                  </h3>
                  <div className="border-border hidden border opacity-20 lg:block" />
                  <h3 className="text-[18px]">
                    {isAllUnlimited ? 'Unlimited UK calls and texts' : ''}
                  </h3>
                </DataPlanDetails>
              </PlanCard>
            </PlainCard>
          );
        })
      )}

      <Divider />
      <div className="bottom-summary-wrapper">
        <TotalCostBottom
          amount={createOrderSummary(basketPlans, planData).totalCost}
        />
        <NextStepLink
          className="bg-primary hover:bg-primary-hover text-bold mt-4 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white"
          text="Continue"
          onClick={() => saveSignupStateToLocalStorage(planSelectionState)}
          href={{
            pathname: ROUTES_CONFIG['add-ons'].path
          }}
        />
      </div>
    </>
  );
}

export default function PlanSelection() {
  const pathname = usePathname();
  const showProgressBar = shouldShowProgressBar(pathname);
  const {
    planSelectionState,
    basketPlans,
    incrementMainPlan,
    decrementMainPlan,
    incrementFamilyPlan,
    decrementFamilyPlan,
    planData,
    isPlanDataPending,
    planDataError
  } = usePlanSelection();

  const [, saveSignupStateToLocalStorage] = useLocalStorage(
    LocalKey.SIGNUP_PLAN_STATE,
    planSelectionState
  );

  if (planDataError) {
    return (
      <PlainCard>
        <Alert message={'We could not fetch flans. Please try again'} />
      </PlainCard>
    );
  }

  return (
    <Wrapper>
      <div className="left-column">
        <ConditionalWrapper className="left-column-inner">
          {showProgressBar && (
            <DesktopOnly>
              <ProgressBar withLabel ariaLabel="sign-up progress bar">
                <ProgressBar.Track>
                  <ProgressBar.Bar className="bg-blueberry mb-6" />
                </ProgressBar.Track>
              </ProgressBar>
            </DesktopOnly>
          )}
          <h1 className="mb-4 lg:mb-0">Plan selection</h1>
          <Divider className="my-6 hidden lg:block" />
          {isPlanDataPending ? (
            <PlanCardSkeleton />
          ) : (
            <PlanCardsSection
              planSelectionState={planSelectionState}
              basketPlans={basketPlans}
              handleBasicPlanIncrement={incrementMainPlan}
              handleBasicPlanDecrement={decrementMainPlan}
              handleFamilyPlanIncrement={incrementFamilyPlan}
              handleFamilyPlanDecrement={decrementFamilyPlan}
              planData={planData}
              saveSignupStateToLocalStorage={saveSignupStateToLocalStorage}
            />
          )}
        </ConditionalWrapper>
      </div>
      <div className="order-1 lg:order-2">
        <OrderSummary
          isPending={isPlanDataPending}
          basketSummary={
            createOrderSummary(basketPlans, planData).basketSummary
          }
          totalCost={createOrderSummary(basketPlans, planData).totalCost}
        />
      </div>
    </Wrapper>
  );
}
