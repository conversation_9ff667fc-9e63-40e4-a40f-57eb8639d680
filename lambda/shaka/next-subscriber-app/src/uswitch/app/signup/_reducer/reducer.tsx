import { PlanState } from '@/src/uswitch/utils/constants';

export const MAX_MAIN_PLAN_QUANTITY = 9;
export const MAX_FAMILY_PLAN_QUANTITY = 9;
export const MIN_MAIN_PLAN_QUANTITY = 1;
export const MIN_FAMILY_PLAN_QUANTITY = 0;

export interface BasketPlan {
  id: string;
  planId: number;
  addonId: number | null;
}

// New nested addon selection structure
export interface ChildPlanAddons {
  addons: number[];
}

export interface PlanAddons {
  addons: number[];
  childPlan?: Record<number, ChildPlanAddons>;
}

export interface AddonSelections {
  [planId: number]: PlanAddons;
}

export interface SignupState extends PlanState {
  mainPlanId: number;
  familyPlanId: number;
  addonSelections: AddonSelections;
}

export const ACTIONS = {
  INCREMENT_MAIN_PLAN: 'INCREMENT_MAIN_PLAN',
  DECREMENT_MAIN_PLAN: 'DECREMENT_MAIN_PLAN',
  INCREMENT_FAMILY_PLAN: 'INCREMENT_FAMILY_PLAN',
  DECREMENT_FAMILY_PLAN: 'DECREMENT_FAMILY_PLAN',
  TOGGLE_ADDON: 'TOGGLE_ADDON'
} as const;

export type SignupAction =
  | { type: typeof ACTIONS.INCREMENT_MAIN_PLAN }
  | { type: typeof ACTIONS.DECREMENT_MAIN_PLAN }
  | { type: typeof ACTIONS.INCREMENT_FAMILY_PLAN }
  | { type: typeof ACTIONS.DECREMENT_FAMILY_PLAN }
  | {
      type: typeof ACTIONS.TOGGLE_ADDON;
      payload: {
        planId: number;
        planIndex: number; // which instance of the plan (0-based)
        addonId: number;
        isFamilyPlan: boolean;
      };
    };

export const initSignupState = (
  mainId: number,
  familyId: number
): SignupState => {
  return {
    mainPlanQuantity: MIN_MAIN_PLAN_QUANTITY,
    familyPlanQuantity: MIN_FAMILY_PLAN_QUANTITY,
    mainPlanId: mainId,
    familyPlanId: familyId,
    addonSelections: {}
  };
};

export const initialState: SignupState = {
  mainPlanQuantity: MIN_MAIN_PLAN_QUANTITY,
  familyPlanQuantity: MIN_FAMILY_PLAN_QUANTITY,
  mainPlanId: 1, // default value
  familyPlanId: 2, // default value
  addonSelections: {}
};

// Helper function to create basket plans with addon selections applied
export const createBasketPlans = (
  mainQuantity: number,
  familyQuantity: number,
  mainPlanId: number,
  familyPlanId: number,
  addonSelections: AddonSelections = {}
): BasketPlan[] => {
  const basketPlans: BasketPlan[] = [];
  
  // Get main plan addons (fallback to empty array if not found)
  const mainPlanAddons = addonSelections[mainPlanId]?.addons || [];
  
  // Create main plan instances
  for (let i = 0; i < mainQuantity; i++) {
    const planInstanceId = generatePlanInstanceId(mainPlanId, i);
    const addonId = mainPlanAddons[i] || 0; // Default to 0 (no addon)
    
    basketPlans.push({
      id: planInstanceId,
      planId: mainPlanId,
      addonId
    });
  }
  
  // Create family plan instances if any
  if (familyQuantity > 0) {
    const familyPlanAddons = addonSelections[mainPlanId]?.childPlan?.[familyPlanId]?.addons || [];
    
    for (let i = 0; i < familyQuantity; i++) {
      const planInstanceId = generatePlanInstanceId(familyPlanId, i);
      const addonId = familyPlanAddons[i] || 0; // Default to 0 (no addon)
      
      basketPlans.push({
        id: planInstanceId,
        planId: familyPlanId,
        addonId
      });
    }
  }
  
  return basketPlans;
};

// Helper function to generate unique plan instance IDs
function generatePlanInstanceId(planId: number, index: number): string {
  return `${planId}_${index}`;
}

// Helper function to get addon for a specific plan instance
export const getAddonForPlanInstance = (
  addonSelections: AddonSelections,
  planId: number,
  planIndex: number,
  isFamilyPlan: boolean,
  mainPlanId?: number
): number => {
  if (isFamilyPlan && mainPlanId) {
    return addonSelections[mainPlanId]?.childPlan?.[planId]?.addons[planIndex] || 0;
  } else {
    return addonSelections[planId]?.addons[planIndex] || 0;
  }
};

// Helper function to check if an addon is selected for a plan instance
export const isAddonSelectedForInstance = (
  addonSelections: AddonSelections,
  planId: number,
  planIndex: number,
  addonId: number,
  isFamilyPlan: boolean,
  mainPlanId?: number
): boolean => {
  const selectedAddonId = getAddonForPlanInstance(
    addonSelections,
    planId,
    planIndex,
    isFamilyPlan,
    mainPlanId
  );
  return selectedAddonId === addonId;
};


export function signupReducer(
  state: SignupState,
  action: SignupAction
): SignupState {
  switch (action.type) {
    case ACTIONS.INCREMENT_MAIN_PLAN:
      return {
        ...state,
        mainPlanQuantity: state.mainPlanQuantity + 1
      };

    case ACTIONS.DECREMENT_MAIN_PLAN:
      return {
        ...state,
        mainPlanQuantity: Math.max(MIN_MAIN_PLAN_QUANTITY, state.mainPlanQuantity - 1)
      };

    case ACTIONS.INCREMENT_FAMILY_PLAN:
      return {
        ...state,
        familyPlanQuantity: Math.min(MAX_FAMILY_PLAN_QUANTITY, state.familyPlanQuantity + 1)
      };

    case ACTIONS.DECREMENT_FAMILY_PLAN:
      return {
        ...state,
        familyPlanQuantity: Math.max(MIN_FAMILY_PLAN_QUANTITY, state.familyPlanQuantity - 1)
      };

    case ACTIONS.TOGGLE_ADDON:
      const { planId, planIndex, addonId, isFamilyPlan } = action.payload;
      const currentSelections = { ...state.addonSelections };
      
      if (isFamilyPlan) {
        // Handle family plan addon selection
        const mainPlanId = state.mainPlanId;
        if (!currentSelections[mainPlanId]) {
          currentSelections[mainPlanId] = { addons: [] };
        }
        if (!currentSelections[mainPlanId].childPlan) {
          currentSelections[mainPlanId].childPlan = {};
        }
        if (!currentSelections[mainPlanId].childPlan![planId]) {
          currentSelections[mainPlanId].childPlan![planId] = { addons: [] };
        }
        
        // Update family plan addon at the specific index
        const familyAddons = [...(currentSelections[mainPlanId].childPlan![planId].addons || [])];
        familyAddons[planIndex] = addonId;
        currentSelections[mainPlanId].childPlan![planId].addons = familyAddons;
      } else {
        // Handle main plan addon selection
        if (!currentSelections[planId]) {
          currentSelections[planId] = { addons: [] };
        }
        
        // Update main plan addon at the specific index
        const mainAddons = [...(currentSelections[planId].addons || [])];
        mainAddons[planIndex] = addonId;
        currentSelections[planId].addons = mainAddons;
      }
      
      return {
        ...state,
        addonSelections: currentSelections
      };


    default:
      return state;
  }
}
