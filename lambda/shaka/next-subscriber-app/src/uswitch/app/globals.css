@import 'tailwindcss';
/*important !!! pull all higher level components that use tailwind classes here !*/
@source "../../../components";

@font-face {
  font-family: FoundersGrotesk;
  font-display: swap;
  src: url('../../../public/font/founders-grotesk-bold.woff2')
}

@theme inline {
  --font-primary: 'Helvetica Neue', ui-sans-serif, system-ui, sans-serif;
  --font-heading: 'FoundersGrotesk', ui-sans-serif, system-ui, sans-serif;
}

@theme {
  /* BREAKPOINTS */
  --breakpoint-custom: 1621px;
  --breakpoint-1200: 1200px;

  /* COLORS */
  --color-primary: #141414;
  --color-primary-hover: #434343;
  --color-tertiary: #79c3ff;
  --color-secondary: #ffffff;
  --color-secondary-hover: #f3f3f4;
  /*--color-secondary-hover: #434343;*/

  --color-blueberry: #79c3ff;
  --color-blueberry-light: #bddcff;
  --color-blueberry-subtle: #e5f3ff;

  --color-mint: #50f094;
  --color-mint-light: #bff8d3;
  --color-mint-subtle: #e5fded;

  --color-lemon: #ffea70;
  --color-lemon-light: #fffac1;
  --color-lemon-subtle: #fffac1;

  --color-gray-subtle: #f3f3f3;
  --color-gray-subtle-tint: #1414141a;

  --color-border-subtle: #141414;
  --color-border: #898989;
  --color-placeholder: #727272;
  --color-success-border: #00ab40;
  --color-warning: #fff6c1;
  --color-warning-border: #f9b728;
  --color-error: #EA0040;
  --color-error-subtle:#FEE8EC;

  /* FONT WEIGHTS */
  --font-weight-heading: 700;
  --font-weight-body: 400;

  /* FONT SIZES */
  /* 1rem = 16px */
  --text-xxxs: 0.875rem; /* 14px */
  --text-default: 1rem; /* 16px */
  --text-xxs: 1.25rem; /* 20px */
  --text-xs: 1.5rem; /* 24px */
  --text-sm: 1.75rem; /* 28px */
  --text-base: 2.25rem; /* 36px */
  --text-lg: 2.5rem; /* 40px */
  --text-xl: 3rem; /* 48px */
  --text-2xl: 3.5rem; /* 56px */

  /* LINE HEIGHT */
  --line-height-xxs: 20px;
  --line-height-xs: 24px;
  --line-height-sm: 28px;
  --line-height-base: 32px;
  --line-height-large: 40px;
  --line-height-xl: 48px;
  --line-height-2xl: 56px;

  /* SPACING */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-7: 32px;
  --spacing-8: 40px;
  --spacing-9: 48px;
  --spacing-10: 64px;
  --spacing-11: 80px;
  --spacing-12: 96px;
  --spacing-13: 104px;
  --spacing-14: 192px;
  --spacing-15: 300px;

  /* SHADOWS */
  --shadow-sm: 0px 1px 3px 1px rgba(20, 20, 20, 0.08),
    0px 1px 2px 0px rgba(20, 20, 20, 0.16);
  --shadow-md: 0px 1px 3px 0px rgba(20, 20, 20, 0.16),
    0px 4px 8px 3px rgba(20, 20, 20, 0.08);
  --shadow-lg: 0px 2px 3px 0px rgba(20, 20, 20, 0.16),
    0px 6px 10px 4px rgba(20, 20, 20, 0.08);
  --shadow-xl: 0px 4px 4px 0px rgba(20, 20, 20, 0.16),
    0px 8px 12px 6px rgba(20, 20, 20, 0.08);
  --shadow-none: none;

  /* PLAN SECTION SPACING */
  --plan-card-padding: var(--spacing-6);
  --plan-card-gap: var(--spacing-4);

  /* BORDER RADIUS */
  --radius-2: 8px;
}

@layer base {
  body {
    @apply font-primary;
  }
}

@layer components {
  .layout {
    @apply lg:bg-gray-subtle mx-auto px-[var(--spacing-6)] py-[var(--spacing-6)] lg:px-[var(--spacing-14)] lg:py-[var(--spacing-7)] xl:py-[var(--spacing-8)];
    /*TODO: use max w instead of padding*/
    /*@apply lg:bg-gray-subtle mx-auto px-[var(--spacing-6)] py-[var(--spacing-6)] lg:px-[var(--spacing-13)] lg:py-[var(--spacing-7)] xl:py-[var(--spacing-8)];*/
  }
  .layout-height {
    @apply min-h-[calc(100dvh-64px)];
  }
  .plain-card {
    @apply bg-secondary rounded-lg border-1 border-gray-300 p-2  lg:p-3;
    box-shadow: 0 4px 8px 3px #14141414, 0 1px 3px 0 #14141429;
  }
  .uswitchLink {
    @apply cursor-pointer rounded-[1px] leading-6 font-[var(--font-primary)] text-[var(--text-xs)] underline underline-offset-2 hover:decoration-2 focus:outline-2 focus:outline-offset-4;
  }
  .outline {
    @apply focus-visible:outline-mint focus:outline-none focus-visible:outline-4 focus-visible:outline-offset-3 focus-visible:outline-solid;
  }
  .chip {
    @apply text-xxxs bg-lemon border-gray-subtle-tint text-primary w-fit rounded border p-[6px] font-bold;
  }
  .input {
    @apply bg-secondary border-border text-placeholder placeholder:text-default placeholder-placeholder w-full rounded-[2px] border p-3
  }
  .plan-card-width-restriction {
    @apply w-[325px] xl:w-[372px];
  }
  .left-column {
    @apply order-2 flex w-full min-w-0 flex-col gap-4 lg:order-1 max-w-[850px];
  }
  .left-column-inner {
    @apply mb-30 p-6 pt-0 lg:mb-0 lg:pt-6;
  }
  .bottom-summary-wrapper {
    @apply bg-secondary border-gray-subtle-tint fixed right-0 bottom-0 left-0 border-t px-6 py-3 shadow-sm lg:static lg:border-none lg:px-0 lg:shadow-none;
  }
  .order-confirmation-layout {
    @media (min-width: 1024px) {
      padding-inline: 2vw;
    }

    @media (min-width: 1224px) {
      padding-inline: 10vw;
    }

    @media (min-width: 1620px) {
      padding-inline: 18vw;
    }

    @media (min-width: 1920px) {
      padding-inline: 23vw;
    }
  }
  details > summary {
    list-style: none;
  }

  details > summary::-webkit-details-marker {
    display: none;
  }

  details > summary::marker {
    display: none;
  }

  details[open] summary .chevron {
    transform: rotate(90deg);
  }

  .chevron {
    transition: transform 0.2s ease;
  }

  details .content {
    animation: accordion-down 0.2s ease-out;
  }

  details:not([open]) .content {
    animation: accordion-up 0.2s ease-out;
  }

  @media (min-width: 1024px) {
    .responsiveMaxWidth {
      max-width: clamp(320px, 83.33vw, 1920px);
    }
  }

  /** {*/
  /*  outline: 1px solid red;*/
  /*}*/

  /* TYPOGRAPHY */

  p {
    font-family: var(--font-primary);
    font-size: var(--text-default);
    line-height: var(--line-height-xs);
    font-weight: var(--font-weight-body);
    color: var(--color-primary);
    opacity: 0.7;
    letter-spacing: 0;
    vertical-align: middle;
  }

  h1 {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    line-height: var(--line-height-large);
    /*font-weight: var(--font-weight-heading);*/
    color: var(--color-primary);
    letter-spacing: 0;
    vertical-align: middle;

    @media (width < 64rem) {
      font-size: var(--text-xs);
      line-height: var(--line-height-base);
    }
  }

  h2 {
    font-family: var(--font-heading);
    font-size: var(--text-xs);
    line-height: var(--line-height-base);
    /*font-weight: var(--font-weight-heading);*/
    color: var(--color-primary);
    letter-spacing: 0;
    vertical-align: middle;
  }

  h3 {
    font-family: var(--font-heading);
    font-size: var(--text-xxs);
    line-height: var(--line-height-xs);
    /*font-weight: var(--font-weight-heading);*/
    color: var(--color-primary);
    letter-spacing: 0;
    vertical-align: middle;
  }
}

/* TODO: support for light/dark theme just in case !!*/
