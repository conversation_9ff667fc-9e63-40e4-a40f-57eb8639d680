'use client';

import { CloseIcon } from '@/icons/icons';
import React from 'react';
import Modal from '@/components/modal/modal';
import { UserPaymentDetailsCard } from '@/src/uswitch/app/dashboard/esim/@accountbilling/_components/payment-details-card';

import { UpcomingBillCard } from '@/src/uswitch/app/dashboard/esim/@accountbilling/_components/upcoming-bull-card';
import { UserPaidBillsList } from '@/src/uswitch/app/dashboard/esim/@accountbilling/_components/paid-bills-list';
import { UserDetailsCard } from '@/src/uswitch/app/dashboard/esim/@accountbilling/_components/details-card';

export default function AccountBilling() {
  return (
    <section className="grid grid-cols-1 gap-y-8 md:gap-x-6 md:gap-y-0 lg:grid-cols-[minmax(0,1.3fr)_minmax(0,0.7fr)] lg:gap-x-8">
      <div>
        <h2 className="mt-4 mb-2">Your bills</h2>
        <UpcomingBillCard />
        <UserPaidBillsList />
      </div>
      <div>
        <h2 className="mt-4 mb-2">Your details</h2>
        <UserDetailsCard />
        <h2 className="mt-8 mb-2">Your payment details</h2>
        <UserPaymentDetailsCard />
      </div>
    </section>
  );
}

interface StandardModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  modalTitle: string;
  modalDescription: string;
  modalSize?: string;
}

export function StandardModal({
  isOpen,
  setIsOpen,
  modalTitle,
  modalDescription,
  modalSize = 'lg:max-w-2xl'
}: StandardModalProps) {
  return isOpen ? (
    <Modal onOpenChange={setIsOpen} open={isOpen}>
      <Modal.Overlay />
      <Modal.Content className={`w-full rounded-lg p-6 ${modalSize}`}>
        <div className="mb-4 flex justify-end">
          <Modal.Close>
            <CloseIcon />
          </Modal.Close>
        </div>
        <Modal.Title className="mb-6 text-xl font-semibold">
          {modalTitle}
        </Modal.Title>
        <Modal.Description>{modalDescription}</Modal.Description>
      </Modal.Content>
    </Modal>
  ) : null;
}
