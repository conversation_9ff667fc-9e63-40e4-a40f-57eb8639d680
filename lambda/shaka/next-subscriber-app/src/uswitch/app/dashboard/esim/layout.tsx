'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import React, { Suspense, useEffect, useState } from 'react';
import { YourPlansLoadingSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/your-plans-skeleton';
import { AccountBillingSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/account-billing-skeleton';
import { HelpSectionSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/help-section-skeleton';

export const TAB_STORAGE_KEY = 'esim-active-tab';

function getTabButtonClass(tab: string, activeTab: string) {
  return `after:bg-primary text-primary relative cursor-pointer px-3 py-2 transition-colors duration-200 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:transition-all after:duration-300 after:ease-out ${
    tab === activeTab
      ? 'font-bold after:scale-x-100'
      : 'hover:text-primary-hover after:scale-x-0'
  }`;
}

export const tabConfig = [
  { value: 'your-plans', label: 'Your plans' },
  { value: 'account-billing', label: 'Account & billing' },
  { value: 'help', label: 'Help' }
] as const;

export type Tab = (typeof tabConfig)[number]['value'];

export const tabFallbacks = {
  'your-plans': <YourPlansLoadingSkeleton withTabs />,
  'account-billing': <AccountBillingSkeleton withTabs />,
  help: <HelpSectionSkeleton withTabs />
};

interface EsimLayoutProps {
  yourplans: React.ReactNode;
  accountbilling: React.ReactNode;
  help: React.ReactNode;
}

function EsimLayoutContent({
  yourplans,
  accountbilling,
  help
}: EsimLayoutProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const savedTab =
    typeof window !== 'undefined'
      ? localStorage.getItem(TAB_STORAGE_KEY)
      : null;
  const currentTab = (searchParams.get('tab') || savedTab) as Tab;
  const [activeTab, setActiveTab] = useState<Tab>(
    currentTab || tabConfig[0].value
  );

  useEffect(() => {
    router.push(`/dashboard/esim?tab=${activeTab}`);
    localStorage.setItem(TAB_STORAGE_KEY, activeTab);
  }, [activeTab, router]);

  const tabContent = {
    'your-plans': yourplans,
    'account-billing': accountbilling,
    help
  };

  return (
    <div>
      <h1 className="mb-6">eSIM</h1>
      <nav title="tab navigation" className="relative mb-4">
        <div className="relative flex border-b border-gray-300">
          {tabConfig.map(({ value, label }) => (
            <button
              key={value}
              onClick={() => setActiveTab(value)}
              className={getTabButtonClass(value, activeTab)}
            >
              {label}
            </button>
          ))}
        </div>
      </nav>

      {tabConfig.map(({ value: tab }) => (
        <div key={tab} className={activeTab === tab ? 'block' : 'hidden'}>
          <Suspense fallback={tabFallbacks[tab]}>{tabContent[tab]}</Suspense>
        </div>
      ))}
    </div>
  );
}

function EsimLayoutFallback() {
  return (
    <div>
      <h1 className="mb-6">eSIM</h1>
      <nav className="relative mb-4">
        <div className="relative flex border-b border-gray-300">
          {tabConfig.map(({ value, label }) => (
            <div
              key={value}
              className="after:bg-primary text-primary relative px-3 py-2 transition-colors duration-200 after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:scale-x-0 after:transition-all after:duration-300 after:ease-out"
            >
              {label}
            </div>
          ))}
        </div>
      </nav>
      <YourPlansLoadingSkeleton />
    </div>
  );
}

export default function EsimLayout(props: EsimLayoutProps) {
  return (
    <Suspense fallback={<EsimLayoutFallback />}>
      <EsimLayoutContent {...props} />
    </Suspense>
  );
}
