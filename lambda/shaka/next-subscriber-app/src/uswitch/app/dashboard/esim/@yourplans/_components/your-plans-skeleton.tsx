import React from 'react';
import { DashboardTabSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/dashboard-tab-skeleton';
import { tabConfig } from '@/src/uswitch/app/dashboard/esim/layout';

export function YourPlansLoadingSkeleton({
  withTabs = false
}: {
  withTabs?: boolean;
}) {
  return (
    <>
      {withTabs && <DashboardTabSkeleton activeTab={tabConfig[0].value} />}
      {/* Mobile Version */}
      <div className="mx-auto mt-4 max-w-sm space-y-4 lg:hidden">
        {/* Main Plan Card */}
        <div className="overflow-hidden rounded-2xl bg-white shadow-sm">
          {/* Header */}
          <div className="relative bg-blue-200 p-4">
            <div className="mb-3 h-6 w-20 animate-pulse rounded bg-gray-300"></div>
            <div className="mb-2 h-10 w-32 animate-pulse rounded bg-gray-300"></div>
            <div className="mb-4 h-4 w-24 animate-pulse rounded bg-gray-300"></div>
            <div className="absolute top-4 right-4">
              <div className="h-8 w-28 animate-pulse rounded bg-black"></div>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-4 p-4">
            {/* Data Usage */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="h-6 w-12 animate-pulse rounded bg-gray-300"></div>
                <div className="h-4 w-20 animate-pulse rounded bg-gray-300"></div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="h-6 w-12 animate-pulse rounded bg-gray-300"></div>
                <div className="h-4 w-20 animate-pulse rounded bg-gray-300"></div>
              </div>
              <div className="h-4 w-40 animate-pulse rounded bg-gray-300"></div>
            </div>

            {/* Warning Banner */}
            <div className="rounded-lg bg-yellow-100 p-3">
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 animate-pulse rounded bg-gray-300"></div>
                <div className="h-4 w-40 animate-pulse rounded bg-gray-300"></div>
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="h-5 w-4 animate-pulse rounded bg-gray-300"></div>
                <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="h-5 w-6 animate-pulse rounded bg-gray-300"></div>
                <div className="h-4 w-24 animate-pulse rounded bg-gray-300"></div>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center space-x-2 pt-2">
              <div className="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
              <div className="h-4 w-12 animate-pulse rounded bg-gray-300"></div>
            </div>
          </div>
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center space-x-2">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className={`h-2 w-2 animate-pulse rounded-full ${i === 0 ? 'bg-gray-800' : 'bg-gray-300'}`}
            ></div>
          ))}
        </div>

        {/* Add New Plan Button */}
        <div className="rounded-lg border-2 border-dashed border-gray-300 p-4">
          <div className="flex items-center justify-center space-x-2">
            <div className="h-6 w-6 animate-pulse rounded-full bg-gray-300"></div>
            <div className="h-4 w-24 animate-pulse rounded bg-gray-300"></div>
          </div>
        </div>
      </div>

      {/* Desktop Version */}
      <div className="mt-4 hidden max-w-4xl grid-cols-2 gap-4 space-x-6 lg:grid">
        {/* Left Side - Plan Card */}
        <div className="relative flex-1">
          {/*<PlanCardSkeletonDesktop className="absolute scale-90 lg:left-26 xl:left-40" />*/}
          {/*<PlanCardSkeletonDesktop className="xl: absolute left-0 scale-90 lg:-left-2" />*/}
          {/*<PlanCardSkeletonDesktop className="absolute lg:left-9 xl:left-11" />*/}
          <PlanCardSkeletonDesktop className="xl:ml-10" />
        </div>

        {/* Right Side - Manage Plans */}
        <div className="ml-auto w-72 space-y-6 xl:w-96">
          {/*<div className="ml-auto w-64 space-y-6 xl:w-80">*/}
          {/* Manage Plans Header */}
          <div className="h-8 w-32 animate-pulse rounded bg-gray-300"></div>

          {/* Subscriptions Section */}
          <div className="space-y-4 rounded-lg bg-white p-6">
            <div className="h-6 w-28 animate-pulse rounded bg-gray-300"></div>
            <div className="flex space-x-3">
              <div className="h-8 w-32 animate-pulse rounded bg-gray-300"></div>
              <div className="h-8 w-24 animate-pulse rounded bg-gray-300"></div>
            </div>
            <div className="h-8 w-20 animate-pulse rounded bg-gray-300"></div>
          </div>

          {/* Plan Section */}
          <div className="space-y-4 rounded-lg bg-white p-6">
            <div className="h-6 w-16 animate-pulse rounded bg-gray-300"></div>
            <div className="flex space-x-3">
              <div className="h-8 w-24 animate-pulse rounded bg-gray-300"></div>
              <div className="h-8 w-32 animate-pulse rounded bg-gray-300"></div>
            </div>
          </div>

          {/* eSIM Section */}
          <div className="space-y-4 rounded-lg bg-white p-6">
            <div className="h-6 w-20 animate-pulse rounded bg-gray-300"></div>
            <div className="flex space-x-3">
              <div className="h-8 w-24 animate-pulse rounded bg-gray-300"></div>
              <div className="h-8 w-36 animate-pulse rounded bg-gray-300"></div>
            </div>
          </div>

          {/* Plan Overview */}
          <div>
            <div className="mb-4 h-8 w-32 animate-pulse rounded bg-gray-300"></div>
            <div className="overflow-hidden rounded-lg bg-white">
              {/* Header */}
              <div className="flex items-center justify-between bg-blue-200 p-4">
                <div className="h-8 w-24 animate-pulse rounded bg-gray-300"></div>
                <div className="h-8 w-12 animate-pulse rounded bg-gray-300"></div>
              </div>

              {/* Tags */}
              <div className="space-y-3 p-4">
                <div className="flex flex-wrap gap-2">
                  <div className="h-8 w-24 animate-pulse rounded bg-yellow-300"></div>
                  <div className="h-8 w-28 animate-pulse rounded bg-yellow-300"></div>
                  <div className="h-8 w-8 animate-pulse rounded bg-yellow-300"></div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <div className="h-8 w-32 animate-pulse rounded bg-yellow-300"></div>
                  <div className="h-8 w-28 animate-pulse rounded bg-yellow-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

function PlanCardSkeletonDesktop({ className }: { className?: string }) {
  return (
    <div className={`w-[325px] space-y-4 xl:w-[372px] ${className}`}>
      <div className="overflow-hidden rounded-2xl bg-white shadow-sm">
        {/* Header */}
        <div className="relative bg-gradient-to-l from-[#e0f0ff] to-[#a3d4ff] p-6">
          <div className="mb-4 h-6 w-24 animate-pulse rounded bg-gray-300"></div>
          <div className="mb-3 h-4 w-40 animate-pulse rounded bg-gray-300"></div>
          <div className="h-5 w-32 animate-pulse rounded bg-gray-300"></div>
          <div className="absolute top-6 right-6">
            <div className="h-8 w-36 animate-pulse rounded bg-black"></div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6 p-6">
          {/* Data Usage */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-16 animate-pulse rounded bg-gray-300"></div>
              <div className="h-5 w-24 animate-pulse rounded bg-gray-300"></div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-8 w-16 animate-pulse rounded bg-gray-300"></div>
              <div className="h-5 w-24 animate-pulse rounded bg-gray-300"></div>
            </div>
            <div className="h-5 w-48 animate-pulse rounded bg-gray-300"></div>
          </div>

          {/* Warning Banner */}
          <div className="rounded-lg bg-yellow-100 p-4">
            <div className="flex items-center space-x-3">
              <div className="h-5 w-5 animate-pulse rounded bg-gray-300"></div>
              <div className="h-5 w-48 animate-pulse rounded bg-gray-300"></div>
            </div>
          </div>

          {/* Stats */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="h-6 w-5 animate-pulse rounded bg-gray-300"></div>
              <div className="h-5 w-20 animate-pulse rounded bg-gray-300"></div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-6 w-8 animate-pulse rounded bg-gray-300"></div>
              <div className="h-5 w-28 animate-pulse rounded bg-gray-300"></div>
            </div>
          </div>

          {/* Status */}
          <div className="flex items-center space-x-3 pt-3">
            <div className="h-3 w-3 animate-pulse rounded-full bg-gray-300"></div>
            <div className="h-5 w-16 animate-pulse rounded bg-gray-300"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
