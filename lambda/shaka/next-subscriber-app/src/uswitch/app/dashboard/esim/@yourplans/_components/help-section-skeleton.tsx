import React from 'react';
import { DashboardTabSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/dashboard-tab-skeleton';
import { tabConfig } from '@/src/uswitch/app/dashboard/esim/layout';

export function HelpSectionSkeleton({
  withTabs = false
}: {
  withTabs?: boolean;
}) {
  return (
    <div>
      {withTabs && <DashboardTabSkeleton activeTab={tabConfig[2].value} />}
      <div className="mx-auto mt-4">
        <div className="flex flex-col gap-8 lg:flex-row">
          {/* Left Column - How can we help */}
          <div className="flex-1 space-y-6">
            {/* How can we help header */}
            <div className="h-8 w-64 animate-pulse rounded bg-gray-800"></div>

            {/* Search box */}
            <div className="flex items-center justify-between rounded-lg border border-gray-300 bg-white p-4">
              <div className="h-5 w-40 animate-pulse rounded bg-gray-400"></div>
              <div className="h-6 w-6 animate-pulse rounded bg-gray-400"></div>
            </div>

            {/* Helpful articles section */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <div className="mb-6 h-8 w-48 animate-pulse rounded bg-gray-800"></div>

              <div className="space-y-4">
                {/* Article items */}
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between border-b border-gray-100 py-4 last:border-b-0"
                  >
                    <div className="h-5 w-56 animate-pulse rounded bg-gray-700"></div>
                    <div className="h-4 w-4 animate-pulse rounded bg-gray-400"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Support Options */}
          <div className="w-full space-y-6 lg:w-80">
            {/* Email support section */}
            <div className="space-y-4 rounded-lg bg-white p-6 shadow-sm">
              <div className="h-8 w-40 animate-pulse rounded bg-gray-800"></div>

              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 animate-pulse rounded-full bg-green-400"></div>
                <div className="h-5 w-20 animate-pulse rounded bg-gray-700"></div>
              </div>

              <div className="rounded bg-yellow-200 px-3 py-2">
                <div className="h-4 w-56 animate-pulse rounded bg-gray-700"></div>
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="h-4 w-44 animate-pulse rounded bg-gray-500"></div>
                <div className="rounded border-2 border-gray-800 px-6 py-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-800"></div>
                </div>
              </div>
            </div>

            {/* Online chat section */}
            <div className="space-y-4 rounded-lg bg-white p-6 shadow-sm">
              <div className="h-8 w-32 animate-pulse rounded bg-gray-800"></div>

              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 animate-pulse rounded-full bg-green-400"></div>
                <div className="h-5 w-20 animate-pulse rounded bg-gray-700"></div>
              </div>

              <div className="rounded bg-yellow-200 px-3 py-2">
                <div className="h-4 w-52 animate-pulse rounded bg-gray-700"></div>
              </div>

              <div className="flex justify-end pt-2">
                <div className="rounded border-2 border-gray-800 px-6 py-2">
                  <div className="h-4 w-20 animate-pulse rounded bg-gray-800"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
