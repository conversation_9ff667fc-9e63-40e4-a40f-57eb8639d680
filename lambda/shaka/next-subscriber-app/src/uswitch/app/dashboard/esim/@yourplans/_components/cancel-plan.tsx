import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import Button from '@/src/uswitch/components/button/button';
import React from 'react';

export function CancelPlan({
  setIsOpen
}: {
  setIsOpen: (isOpen: boolean) => void;
}) {
  // mutation to handle plan cancelation here
  // need id of some sort ?
  // passed from the active card in carousel ?
  return (
    <>
      <p>
        Once you confirm your cancellation, this plan will remain active until
        20/09/2025 and then will be cancelled.
      </p>
      <br />
      <p>
        To keep your number for your next provider please text “PAC” to 65075.
      </p>
      <Divider className="my-4" />
      <div className="flex flex-wrap gap-4">
        <Button
          onClick={() => setIsOpen(false)}
          variant="secondary"
          className="grow sm:basis-[calc(50%-8px)]"
        >
          I’ve changed my mind
        </Button>
        <Button variant="primary" className="grow sm:basis-[calc(50%-8px)]">
          Cancel my plan
        </Button>
      </div>
    </>
  );
}

// function CancelPlanConfirmation() {
//   return (
//     <FormAlert
//       variant="success"
//       title="Your plan has been cancelled"
//       messagesStyles="space-y-4"
//       messages={[
//         `yourPlanRemainsActiveUntil: You can still use your plan until 20/09/2025.`,
//         `lastPaymentDate: The last payment for this plan will be taken on 20/09/2025.`,
//         `keepYourNumber: To keep your number for your next provider please text “PAC” to 65075.`
//       ]}
//     />
//   );
// }
