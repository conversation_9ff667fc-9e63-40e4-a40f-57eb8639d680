import React from 'react';
import { DashboardTabSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/dashboard-tab-skeleton';
import { tabConfig } from '@/src/uswitch/app/dashboard/esim/layout';

export function AccountBillingSkeleton({
  withTabs = false
}: {
  withTabs?: boolean;
}) {
  return (
    <div>
      {withTabs && <DashboardTabSkeleton activeTab={tabConfig[1].value} />}
      <div className="mx-auto max-w-6xl">
        <div className="mt-4 flex flex-col gap-8 lg:flex-row">
          {/* Left Column - Your bills */}
          <div className="flex-1 space-y-6">
            {/* Your bills header */}
            <div className="h-6 w-32 animate-pulse rounded bg-gray-800"></div>

            {/* Upcoming bills section */}
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
              <div className="mb-6 border-b border-gray-200 pb-4">
                <div className="h-6 w-40 animate-pulse rounded bg-gray-800"></div>
              </div>

              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="h-4 w-24 animate-pulse rounded bg-gray-400"></div>
                    <div className="h-2 w-2 animate-pulse rounded-full bg-yellow-400"></div>
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                  </div>
                  <div className="h-10 w-16 animate-pulse rounded bg-gray-800"></div>
                </div>

                <div className="space-y-2 text-right">
                  <div className="h-4 w-32 animate-pulse rounded bg-gray-400"></div>
                  <div className="h-4 w-24 animate-pulse rounded bg-gray-400"></div>
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-400"></div>
                  <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                </div>
              </div>
            </div>

            {/* Paid bills section */}
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
              <div className="mb-6 border-b border-gray-200 pb-4">
                <div className="h-6 w-24 animate-pulse rounded bg-gray-800"></div>
              </div>

              {/* Bill entries */}
              <div className="space-y-6">
                {/* First bill - paid */}
                <div className="flex items-center justify-between border-b border-gray-100 pb-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-24 animate-pulse rounded bg-gray-400"></div>
                      <div className="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
                      <div className="h-4 w-12 animate-pulse rounded bg-gray-400"></div>
                    </div>
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-800"></div>
                  </div>
                  <div className="flex space-x-4">
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                    <div className="h-4 w-16 animate-pulse rounded bg-gray-400"></div>
                  </div>
                </div>

                {/* Second bill - pending */}
                <div className="flex items-center justify-between border-b border-gray-100 pb-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                      <div className="h-2 w-2 animate-pulse rounded-full bg-yellow-400"></div>
                      <div className="h-4 w-16 animate-pulse rounded bg-gray-400"></div>
                    </div>
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-800"></div>
                  </div>
                  <div className="flex space-x-4">
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                    <div className="h-4 w-16 animate-pulse rounded bg-gray-400"></div>
                  </div>
                </div>

                {/* Third bill - failed */}
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-18 animate-pulse rounded bg-gray-400"></div>
                      <div className="h-2 w-2 animate-pulse rounded-full bg-red-400"></div>
                      <div className="h-4 w-12 animate-pulse rounded bg-gray-400"></div>
                    </div>
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-800"></div>
                  </div>
                  <div className="flex space-x-4">
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                    <div className="h-4 w-16 animate-pulse rounded bg-gray-400"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="mt-2 w-full space-y-6 lg:w-80">
            {/* Your details section */}
            <div>
              <div className="mb-4 h-6 w-32 animate-pulse rounded bg-gray-800"></div>
              <div className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <div className="h-6 w-28 animate-pulse rounded bg-gray-800"></div>
                <div className="h-4 w-48 animate-pulse rounded bg-gray-400"></div>
                <div className="h-4 w-24 animate-pulse rounded bg-gray-400"></div>
                <div className="h-4 w-32 animate-pulse rounded bg-gray-400"></div>
              </div>
            </div>

            {/* Your payment details section */}
            <div>
              <div className="mb-4 h-6 w-48 animate-pulse rounded bg-gray-800"></div>
              <div className="space-y-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                {/* Card details */}
                <div className="rounded-lg bg-gray-50 p-4">
                  <div className="h-4 w-44 animate-pulse rounded bg-gray-400"></div>
                </div>

                {/* Powered by section */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-400"></div>
                    <div className="h-6 w-16 animate-pulse rounded bg-gray-400"></div>
                  </div>

                  {/* Edit Payment button */}
                  <div className="w-fit rounded border-2 border-gray-800 p-3">
                    <div className="h-4 w-24 animate-pulse rounded bg-gray-800"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
