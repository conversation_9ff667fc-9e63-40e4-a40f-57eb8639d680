import { EnvelopeIcon } from '@/icons/icons';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import Button from '@/src/uswitch/components/button/button';
import React, { useState } from 'react';
import { Email, validateEmailForm } from '@/src/uswitch/schemas/schemas';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { AxiosError } from 'axios';
import { Alert } from '@/components/alert/alert';
import { useMutation } from '@tanstack/react-query';
import { useAuth } from '@/auth/hooks/use-auth';
import { subscripionsService } from '@/services/subscriptionsService';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';

export function EsimShare() {
  // need id of some sort ?
  // passed from the active card in carousel ?
  // dynamic placeholder based on the selected plan name ?

  const [emailError, setEmailError] = useState('');
  const [successMessage, setSuccessMessage] = useState<null | boolean>(null);
  const { apiClient } = useAuth();

  const {
    mutate: shareEsim,
    isPending,
    error: mutationError
  } = useMutation({
    mutationFn: async (data: Email) =>
      subscripionsService.shareEsim(apiClient, data),
    onSuccess: () => {
      setSuccessMessage(true);
    },
    onError: (error) => {
      const [networkError] = standariseNetworkError(error);
      setEmailError(networkError);
    }
  });

  const handleEsimShare = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;

    const result = validateEmailForm(email);

    try {
      if (result.success) {
        if (result.data) {
          shareEsim(result.data);
        }
      } else {
        const formattedErrors = result.error?.format() || {};
        const [flatErrors] = flattenValidationErrors(formattedErrors);
        setEmailError(flatErrors);
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error('Form submission error:', error);
        setEmailError(error.message);
      }
    }
  };

  const error = emailError || mutationError?.message;

  return (
    <form onSubmit={handleEsimShare}>
      {successMessage ? (
        <FormAlert
          className="mb-4 lg:my-6"
          title=""
          variant="success"
          messages={[
            `message: The email has been shared successfully. You may now close this window.`
          ]}
          dismissible={false}
        />
      ) : (
        <>
          <div className="relative">
            <input
              type="email"
              id="email"
              name="email"
              autoComplete="email"
              placeholder="Email address"
              className="input pl-9"
            />
            <EnvelopeIcon className="absolute top-[13px] left-3" />
          </div>
          {error && (
            <Alert
              variant="error"
              message={error}
              align="left"
              className="my-4"
            />
          )}
          <Divider className="mt-4" />
          <Button
            isLoading={isPending}
            disabled={isPending}
            className="w-full"
            variant="primary"
          >
            Email eSIM
          </Button>
        </>
      )}
    </form>
  );
}
