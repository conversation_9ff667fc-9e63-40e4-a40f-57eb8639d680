'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { CogIcon, ESimIcon, MiniGridIcon, StatsIcon } from '@/icons/icons';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';

const navigationItems = [
  {
    name: 'Overview',
    icon: <MiniGridIcon />,
    href: ROUTES_CONFIG['dashboard-overview'].path
  },
  {
    name: 'Track',
    icon: <StatsIcon />,
    href: ROUTES_CONFIG['dashboard-track'].path
  },
  {
    name: 'eSIM',
    icon: <ESimIcon />,
    href: ROUTES_CONFIG['dashboard-esim'].path
  },
  {
    name: 'Manage',
    icon: <CogIcon />,
    href: ROUTES_CONFIG['dashboard-manage'].path
  }
] as const;

type NavigationItem = (typeof navigationItems)[number]['name'];

export function DasboardSidebarNav() {
  const [activeItem, setActiveItem] = useState<NavigationItem | ''>('');
  const pathname = usePathname();

  useEffect(() => {
    const currentPath = pathname;

    const matchedItem = navigationItems.find((item) => {
      const [itemPath] = item.href.split('?');
      return currentPath.startsWith(itemPath);
    });

    if (matchedItem) {
      setActiveItem(matchedItem.name);
    } else {
      setActiveItem('Overview');
    }
  }, [pathname]);

  return (
    <>
      {/* Desktop Sidebar */}
      {/*<aside className="bg-secondary hidden h-fit rounded-2xl p-2 shadow-sm lg:flex!">*/}
      <aside className="bg-secondary hidden h-fit rounded-2xl p-2 shadow-sm lg:flex">
        <nav title="sidebar" className="flex-1 space-y-2">
          {navigationItems.map((item) => {
            const isActive = activeItem === item.name;

            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setActiveItem(item.name)}
                className={`text-default flex items-center gap-1 rounded-lg p-2 transition-colors ${
                  isActive
                    ? 'bg-gray-subtle text-primary'
                    : 'text-primary hover:bg-gray-subtle opacity-70 hover:opacity-100'
                }`}
              >
                {item.icon}
                <span className="font-medium">{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Mobile Bottom Navigation */}
      <div className="border-gray-subtle-tint bg-secondary fixed right-0 bottom-0 left-0 z-40 border-t lg:hidden">
        <nav className="flex items-center justify-around py-2 sm:justify-center sm:gap-8">
          {navigationItems.map((item) => {
            const isActive = activeItem === item.name;
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setActiveItem(item.name)}
                className={`flex flex-col items-center rounded-md px-6 py-2 transition-colors ${
                  isActive
                    ? 'bg-gray-subtle text-primary'
                    : 'text-primary hover:bg-gray-subtle opacity-70 hover:opacity-100'
                }`}
              >
                {item.icon}
                <span className="text-xxxs font-medium">{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </>
  );
}
