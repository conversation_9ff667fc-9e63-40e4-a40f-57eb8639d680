import { OrderItemWithQuantity } from '@/src/uswitch/utils/constants';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { BasketPlan } from '@/src/uswitch/app/signup/_reducer/reducer';
import type {
  AddonSelections,
  PlanAddons,
  ChildPlanAddons
} from '@/src/uswitch/app/signup/_reducer/reducer';
import {
  Addon,
  MainPlan,
  SingleSubscription
} from '@/src/uswitch/schemas/schemas';

export function calculateTotalCost(
  orderItems: OrderItemWithQuantity[]
): number {
  return orderItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );
}

export function shouldShowProgressBar(pathname: string): boolean {
  return (
    pathname !== ROUTES_CONFIG['order-confirmation'].path &&
    pathname !== ROUTES_CONFIG['payment-error'].path &&
    pathname !== ROUTES_CONFIG['sign-in'].path
  );
}

// consider moving to global helpers
export function getFieldError(fieldName: string, errors: string[]) {
  const error = errors.find((error) => error.startsWith(`${fieldName}:`));
  return error ? error.replace(`${fieldName}: `, '') : null;
}

export function getSignupPageName(pathname: string): string {
  if (typeof pathname !== 'string' || !pathname) {
    return '';
  }
  const match = pathname.match(/\/signup\/(.*?)(?:\?|#|$)/);
  if (match && match[1]) {
    return match[1]
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, (c) => c.toUpperCase());
  }
  return '';
}

export type Accordion = {
  id: string;
  planId: number;
  planType: string;
  planIndex: number;
  planDescription: string;
  addons: Addon[];
};

export function getBasketSummaryKey(
  planId: number,
  addonId: number | null
): string {
  return `${planId}|${addonId ?? 'null'}`;
}

export function groupBasketPlans(
  basketPlans: BasketPlan[]
): Record<string, number> {
  const summaries: Record<string, number> = {};

  basketPlans.forEach((basketPlan) => {
    const key = getBasketSummaryKey(basketPlan.planId, basketPlan.addonId);
    summaries[key] = (summaries[key] || 0) + 1;
  });

  return summaries;
}

export interface BasketSummaryItem {
  planId: number;
  planName: string;
  planPrice: number;
  addonId: number | null;
  addonName: string | null;
  addonPrice: number;
  quantity: number;
  totalPrice: number;
  displayName: string;
  hasAddon: boolean;
  europeData: number;
}

export function buildPlanAndAddonLookups(planData: MainPlan[]): {
  planLookup: Map<number, any>;
  addonLookup: Map<number, any>;
} {
  const planLookup = new Map();
  const addonLookup = new Map();

  planData.forEach((plan) => {
    planLookup.set(plan.id, plan);

    // Add family plans to lookup
    if (plan.family_plans) {
      plan.family_plans.forEach((familyPlan) => {
        planLookup.set(familyPlan.id, familyPlan);
      });
    }

    // Build addon lookup for this plan
    if (plan.travel_addons) {
      plan.travel_addons.forEach((addon) => {
        addonLookup.set(addon.id, addon);
      });
    }

    // Build addon lookup for family plans
    if (plan.family_plans) {
      plan.family_plans.forEach((familyPlan) => {
        if (familyPlan.travel_addons) {
          familyPlan.travel_addons.forEach((addon) => {
            addonLookup.set(addon.id, addon);
          });
        }
      });
    }
  });

  return { planLookup, addonLookup };
}

export function parseBasketPlanKey(key: string): {
  planId: number;
  addonId: number | null;
} {
  const [planIdStr, addonIdStr] = key.split('|');
  const planId = parseInt(planIdStr, 10);
  const addonId = addonIdStr === 'null' ? null : parseInt(addonIdStr, 10);

  return { planId, addonId };
}

// both fn to be tested !!!!
export function generatePlanInstanceId(planId: number, index: number): string {
  return `${planId}_${index}`;
}

export function createSummaryItemsFromGroupedPlans(
  groupedPlans: Record<string, number>,
  planLookup: Map<number, any>,
  addonLookup: Map<number, any>
): BasketSummaryItem[] {
  const summaryItems: BasketSummaryItem[] = [];

  Object.entries(groupedPlans).forEach(([key, quantity]) => {
    const { planId, addonId } = parseBasketPlanKey(key);

    const plan = planLookup.get(planId);
    if (!plan) {
      return;
    }

    const addon = addonId ? addonLookup.get(addonId) || null : null;
    const addonPrice = addon?.price || 0;
    const totalPrice = (plan.price + addonPrice) * quantity;

    const displayName = plan.name;

    summaryItems.push({
      planId,
      planName: plan.name,
      planPrice: plan.price,
      addonId,
      addonName: addon?.name || null,
      addonPrice,
      quantity,
      totalPrice,
      displayName,
      hasAddon: addon !== null,
      europeData: plan.allowances?.europe_data
    });
  });

  return summaryItems;
}

export function createBasketSummaryItems(
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): BasketSummaryItem[] {
  if (!planData || basketPlans.length === 0) {
    return [];
  }

  const groupedPlans = groupBasketPlans(basketPlans);
  const { planLookup, addonLookup } = buildPlanAndAddonLookups(planData);

  return createSummaryItemsFromGroupedPlans(
    groupedPlans,
    planLookup,
    addonLookup
  );
}

export function createOrderItemsFromBasket(
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): OrderItemWithQuantity[] {
  const summaryItems = createBasketSummaryItems(basketPlans, planData);

  return summaryItems.map((item) => ({
    id: item.addonId
      ? parseInt(`${item.planId}${item.addonId}`, 10)
      : item.planId,
    name: item.displayName,
    price: item.planPrice + item.addonPrice,
    quantity: item.quantity,
    displayName: item.displayName
  }));
}

export type OrderSummary = {
  orderItems: OrderItemWithQuantity[];
  totalCost: number;
  basketSummary: BasketSummaryItem[];
};

export function createOrderSummary(
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): OrderSummary {
  const orderItems = createOrderItemsFromBasket(basketPlans, planData);

  const totalCost = calculateTotalCost(orderItems);

  return {
    orderItems,
    totalCost,
    basketSummary: createBasketSummaryItems(basketPlans, planData)
  };
}

export function buildPlanLookupForAccordion(
  planData: MainPlan[]
): Map<number, any> {
  const planLookup = new Map();

  planData.forEach((plan) => {
    planLookup.set(plan.id, plan);
    if (plan.family_plans) {
      plan.family_plans.forEach((familyPlan) => {
        planLookup.set(familyPlan.id, familyPlan);
      });
    }
  });

  return planLookup;
}

export function createAccordionItemFromBasketPlan(
  basketPlan: BasketPlan,
  plan: MainPlan,
  idPrefix: string,
  planIndex: number
): Accordion & { planInstanceId: string } {
  const europeData = plan.allowances?.europe_data || 10;

  const noneAddon = {
    id: 0,
    name: 'None',
    region: '',
    allowances: { data: 0 },
    price: 0
  };
  const addons = [noneAddon, ...(plan.travel_addons || [])];

  return {
    id: `${idPrefix}-${basketPlan.id}`,
    planId: basketPlan.planId,
    planType: plan.name,
    planIndex,
    planDescription: `${europeData}GB roaming in Europe already included`,
    addons,
    planInstanceId: basketPlan.id
  };
}

export function createAccordionItemsFromBasketPlans(
  basketPlans: BasketPlan[],
  planLookup: Map<number, any>,
  idPrefix: string
): (Accordion & { planInstanceId: string })[] {
  const items: (Accordion & { planInstanceId: string })[] = [];

  basketPlans.forEach((basketPlan, index) => {
    const plan = planLookup.get(basketPlan.planId);

    if (!plan) {
      return;
    }

    const accordionItem = createAccordionItemFromBasketPlan(
      basketPlan,
      plan,
      idPrefix,
      index
    );

    items.push(accordionItem);
  });

  return items;
}

export function generateAccordionItemsFromBasket(
  idPrefix: string,
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): (Accordion & { planInstanceId: string })[] {
  if (!planData || basketPlans.length === 0) {
    return [];
  }

  const planLookup = buildPlanLookupForAccordion(planData);

  return createAccordionItemsFromBasketPlans(basketPlans, planLookup, idPrefix);
}

export function getEffectiveOpenAccordionId(
  openAccordionId: string | null,
  accordionItems: { id: string; addons: Addon[] }[]
): string | null {
  return (
    openAccordionId ??
    accordionItems.find((item) => item.addons.length > 0)?.id ??
    null
  );
}

export function constructAriaLabel(
  isNoneAddon: boolean,
  price: number,
  title?: string,
  description?: string,
  value?: string | number,
  originalPrice?: number | string
): string {
  return [
    title ? title + ', ' : '',
    description ? description + ', ' : '',
    value ? `${value}GB data, ` : '',
    isNoneAddon
      ? 'None'
      : `£${price} a month${originalPrice ? ', was £' + originalPrice : ''}`
  ].join('');
}

export function initialiseMainPlanAddons(
  addonSelections: AddonSelections,
  planId: number
): AddonSelections {
  if (addonSelections[planId]) return addonSelections;

  return {
    ...addonSelections,
    [planId]: { addons: [] }
  };
}

export function initialiseChildPlanAddonsForFamilyPlan(
  addonSelections: AddonSelections,
  mainPlanId: number,
  childPlanId: number
): AddonSelections {
  const addonSelectionsWithMainPlan = addonSelections[mainPlanId]
    ? addonSelections
    : {
        ...addonSelections,
        [mainPlanId]: { addons: [] }
      };

  const mainPlanAddons = addonSelectionsWithMainPlan[mainPlanId];

  const mainPlanHasChildPlan = !!mainPlanAddons.childPlan;
  const addonSelectionsWithChildPlan = mainPlanHasChildPlan
    ? addonSelectionsWithMainPlan
    : {
        ...addonSelectionsWithMainPlan,
        [mainPlanId]: { ...mainPlanAddons, childPlan: {} }
      };

  const mainPlanAddonsWithChildPlan = addonSelectionsWithChildPlan[mainPlanId];

  const childPlanExists =
    !!mainPlanAddonsWithChildPlan.childPlan?.[childPlanId];

  if (childPlanExists) {
    return addonSelectionsWithChildPlan;
  }

  return {
    ...addonSelectionsWithChildPlan,
    [mainPlanId]: {
      ...mainPlanAddonsWithChildPlan,
      childPlan: {
        ...mainPlanAddonsWithChildPlan.childPlan,
        [childPlanId]: { addons: [] }
      }
    }
  };
}

export function updateAddonForPlanInstance(
  addonSelections: AddonSelections,
  planId: number,
  planIndex: number,
  addonId: number
): AddonSelections {
  const next = initialiseMainPlanAddons(addonSelections, planId);
  const plan = next[planId];
  const newAddons = [...(plan.addons || [])];
  newAddons[planIndex] = addonId;
  return {
    ...next,
    [planId]: { ...plan, addons: newAddons }
  };
}

function buildUpdatedChildPlan(
  childPlan: Record<number, ChildPlanAddons> | undefined,
  planId: number,
  planIndex: number,
  addonId: number
): Record<number, ChildPlanAddons> {
  const prevChild = childPlan ? childPlan[planId] : undefined;
  const prevAddons = prevChild ? prevChild.addons : [];
  const newAddons = [...prevAddons];
  newAddons[planIndex] = addonId;
  return {
    ...(childPlan || {}),
    [planId]: { addons: newAddons }
  };
}

export function updateAddonForFamilyPlanInstance(
  addonSelections: AddonSelections,
  mainPlanId: number,
  planId: number,
  planIndex: number,
  addonId: number
): AddonSelections {
  const next = initialiseChildPlanAddonsForFamilyPlan(
    addonSelections,
    mainPlanId,
    planId
  );
  const main = next[mainPlanId];
  const updatedChildPlan = buildUpdatedChildPlan(
    main.childPlan,
    planId,
    planIndex,
    addonId
  );
  const updatedMain: PlanAddons = {
    ...main,
    childPlan: updatedChildPlan
  };
  return {
    ...next,
    [mainPlanId]: updatedMain
  };
}

export function getDesiredCodeChangeDate(
  day?: string,
  month?: string,
  year?: string
) {
  if (day && month && year) {
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  return null;
}

const usageExceeded = (used: number, allowance: number) => {
  const THRESHOLD = 0.75;
  if (allowance === 0) return false;

  const usagePercentage = used / allowance;
  return usagePercentage > THRESHOLD;
};

export function determineSubscriptionAction(subscription: SingleSubscription) {
  if (!subscription) {
    return 'No Button';
  }

  const simType = subscription.current_sim?.sim_type ?? '';
  const serviceType = subscription.current_sim?.service_type ?? '';
  const planDetails = subscription.current_plan?.plan_details ?? {};
  const usage = subscription.usage ?? {};

  if (simType !== 'esim') {
    return 'No Button';
  }

  if (serviceType === 'local') {
    const ukDataExceeded = usageExceeded(
      usage.uk?.data?.used ?? 0,
      planDetails.data_allowance_gb ?? 0
    );
    const ukVoiceExceeded = usageExceeded(
      usage.uk?.voice?.used ?? 0,
      planDetails.voice_allowance_minutes ?? 0
    );
    const ukSmsExceeded = usageExceeded(
      usage.uk?.sms?.used ?? 0,
      planDetails.sms_allowance ?? 0
    );

    const localUsageExceeded =
      ukDataExceeded || ukVoiceExceeded || ukSmsExceeded;

    if (localUsageExceeded) {
      return 'Upgrade to Unlimited';
    } else {
      return 'Keep Your Number';
    }
  }

  if (serviceType !== 'local') {
    // if (serviceType == 'roaming') { - check with backend
    const europeDataExceeded = usageExceeded(
      usage.europe?.data?.used ?? 0,
      planDetails.eu_data_allowance_gb ?? 0
    );
    const europeVoiceExceeded = usageExceeded(
      usage.europe?.voice?.used ?? 0,
      planDetails.voice_allowance_minutes ?? 0
    );
    const europeSmsExceeded = usageExceeded(
      usage.europe?.sms?.used ?? 0,
      planDetails.sms_allowance ?? 0
    );

    const roamingUsageExceeded =
      europeDataExceeded || europeVoiceExceeded || europeSmsExceeded;

    if (roamingUsageExceeded) {
      return 'Add More Data';
    }
  }

  return 'No Button';
}
