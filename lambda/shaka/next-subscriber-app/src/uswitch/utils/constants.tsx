import { CameraIcon, SimIcon } from '@/icons/icons';

export interface GlobalRoamingPackage {
  id: number;
  data: number;
  price: number;
}

// export const globalRoamingPackages: GlobalRoamingPackage[] = [
//   {
//     id: 1,
//     data: 1,
//     price: 1
//   },
//   {
//     id: 2,
//     data: 2,
//     price: 2
//   },
//   {
//     id: 3,
//     data: 3,
//     price: 3
//   },
//   {
//     id: 4,
//     data: 10,
//     price: 5
//   }
// ];

// export const basicPlanCardData = {
//   plan: {
//     title: 'Unlimited',
//     description: 'data',
//     footerText: '5G network speeds'
//   },
//   pricing: {
//     title: '£15.00',
//     description: 'a month',
//     footerText: '30 day rolling contract'
//   }
// };

// export const familyPlanCardData = {
//   plan: {
//     title: '40GB',
//     description: 'data',
//     footerText: '5G network speeds'
//   },
//   pricing: {
//     title: '£8.00',
//     description: 'a month',
//     footerText: '30 day rolling contract'
//   }
// };

export interface PlanState {
  mainPlanQuantity: number;
  familyPlanQuantity: number;
}

export interface OrderItem {
  id: number;
  name: string;
  price: number;
  description?: Record<string, string>;
}

export interface OrderItemWithQuantity extends OrderItem {
  quantity: number;
  displayName: string;
}

export const planOrderItems: OrderItem[] = [
  {
    id: 1,
    name: 'Unlimited SIM plan',
    price: 15,
    description: {
      unlimitedTexts: 'Unlimited UK texts',
      unlimitedMinutes: 'Unlimited UK minutes',
      unlimitedData: 'Unlimited UK data (we might limit you after 650GB)',
      euRoaming: 'European roaming up to 20GB per month.',
      euRoamingLimit: 'Limited to a 10 days per trip.',
      fiveG: '5G network speeds',
      cancelAnytime: 'Cancel whenever you want',
      keepNumber: 'Switch and keep your number in one simple step'
    }
  },
  {
    id: 2,
    name: '40GB SIM plan',
    price: 8,
    description: {
      unlimitedTexts: 'Unlimited UK texts',
      unlimitedMinutes: 'Unlimited UK minutes',
      unlimitedData: 'Unlimited UK data (we might limit you after 650GB)',
      euRoaming: 'European roaming up to 20GB per month.',
      euRoamingLimit: 'Limited to a 10 days per trip.',
      fiveG: '5G network speeds',
      cancelAnytime: 'Cancel whenever you want',
      keepNumber: 'Switch and keep your number in one simple step'
    }
  }
];

export const helpSectionCardStatus = {
  available: {
    color: 'bg-success-border',
    text: 'Available'
  }
};

export const infoGrid = [
  // {
  //   icon: WifiIcon,
  //   text: 'Make sure you are connected to the internet'
  // },
  {
    icon: CameraIcon,
    text: 'Scan the QR code with your phone'
  },
  {
    icon: SimIcon,
    text: 'Click “Add eSIM” and finish the set-up process'
  }
];

export const unlimitedPlanPerks = [
  'Unlimited texts',
  'Unlimited calls',
  '5G',
  'Unlimited UK data',
  '100GB EU data'
];
