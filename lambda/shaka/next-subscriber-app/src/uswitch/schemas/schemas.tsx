import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';
import { z } from 'zod';
import { flattenValidationErrors } from '@/utils/helpers';

// Schema for the Addon interface
export const addonSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, 'Name is required'),
  region: z.string().min(1, 'Region is required'),
  allowances: z.object({
    data: z.number().nonnegative('Data allowance must be a non-negative number')
  }),
  price: z.number().nonnegative('Price must be a non-negative number')
});

// Schema for the FamilyPlan interface
export const familyPlanSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, 'Name is required'),
  price: z.number().nonnegative('Price must be a non-negative number'),
  allowances: z.object({
    data: z.union([z.number(), z.string()]),
    calls: z.string(),
    texts: z.string(),
    europe_data: z
      .number()
      .nonnegative('Europe data must be a non-negative number')
  }),
  travel_addons: z.array(addonSchema).optional()
});

// Schema for the MainPlan interface
export const mainPlanSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1, 'Name is required'),
  allowances: z.object({
    data: z.union([z.number(), z.string()]),
    calls: z.string(),
    texts: z.string(),
    europe_data: z
      .number()
      .nonnegative('Europe data must be a non-negative number')
  }),
  travel_addons: z.array(addonSchema),
  price: z.number().nonnegative('Price must be a non-negative number'),
  family_plans: z.array(familyPlanSchema).optional()
});

export const plansApiResponseSchema = z.array(mainPlanSchema);

export const simsApiResponseSchema = z.array(
  z.object({
    serial_number: z.string(),
    status: z.string(),
    activation_date: z.string().nullable(),
    current_msisdn: z.string(),
    sim_type: z.enum(['physical', 'esim']),
    service_type: z.string(),
    esim_data: z.object({
      qr_code_base64: z.string().base64(),
      qr_code_image: z.string().url(),
      sm_dp_address: z.string(),
      activation_code: z.string(),
      ios_universal_link: z.string().url(),
      android_activation_data: z.string()
    })
  })
);

const PlanDetailsSchema = z.object({
  data_allowance_gb: z.number().int().nonnegative(),
  voice_allowance_minutes: z.number().int().nonnegative(),
  sms_allowance: z.number().int().nonnegative(),
  eu_roaming_enabled: z.boolean(),
  row_roaming_enabled: z.boolean(),
  eu_data_allowance_gb: z.number().int().nonnegative(),
  row_data_allowance_gb: z.number().int().nonnegative(),
  bundle_id: z.string()
});

const PlanSchema = z.object({
  id: z.number().int().positive(),
  name: z.string(),
  plan_details: PlanDetailsSchema,
  reference_id: z.string().nullable()
});

const UsageDataSchema = z.object({
  used: z.number(),
  remaining: z.number().nullable()
});

const RegionalUsageSchema = z.object({
  data: UsageDataSchema,
  voice: UsageDataSchema,
  sms: UsageDataSchema
});

const RoamingUsageSchema = z.object({
  daysUsed: z.number().int().nonnegative(),
  daysAllowed: z.number().int().positive()
});

const UsageSchema = z.object({
  uk: RegionalUsageSchema,
  europe: RegionalUsageSchema.optional(),
  period_start: z.string().datetime(),
  period_end: z.string().datetime(),
  billing_cycle_period: z.enum(['monthly', 'yearly'])
});

const ESimDataSchema = z.object({
  qr_code_base64: z.string(),
  qr_code_image: z.string().url(),
  sm_dp_address: z.string(),
  activation_code: z.string(),
  ios_universal_link: z.string().url(),
  android_activation_data: z.string()
});

const CurrentSimSchema = z.object({
  serial_number: z.string().length(20),
  status: z.enum(['active', 'inactive', 'suspended']),
  activation_date: z.string().datetime().nullable(),
  current_msisdn: z.string(),
  sim_type: z.enum(['physical', 'esim']),
  service_type: z.string(),
  esim_data: ESimDataSchema.nullable()
});

const SubscriptionSchema = z.object({
  id: z.number().int().positive(),
  subscriber: z.number().int().positive(),
  current_plan: PlanSchema,
  usage: UsageSchema,
  roamingUsage: RoamingUsageSchema,
  current_sim: CurrentSimSchema,
  current_msisdn: z.string(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime().nullable(),
  status: z.string(),
  service_type: z.string(),
  current_billing_cycle_end: z.string().datetime(),
  next_billing_cycle_start: z.string().datetime(),
  billing_cycle_period: z.enum(['monthly', 'yearly']),
  reference_id: z.string().nullable()
});

export const SubscriptionsSchema = z.array(SubscriptionSchema);

// Type inference from the schema
export type Addon = z.infer<typeof addonSchema>;
export type FamilyPlan = z.infer<typeof familyPlanSchema>;
export type MainPlan = z.infer<typeof mainPlanSchema>;
export type PlansApiResponse = z.infer<typeof plansApiResponseSchema>;
export type SimsApiResponse = z.infer<typeof simsApiResponseSchema>;
export type SubscriptionsApiResponse = z.infer<typeof SubscriptionsSchema>;
export type SingleSubscription = z.infer<typeof SubscriptionSchema>;
export type RoamingUsage = z.infer<typeof RoamingUsageSchema>;

const PAC_REGEX = /^[A-Za-z]{3}\d{6}$/;
const STAC_REGEX = /^\d{6}[A-Za-z]{3}$/;

export const pacCodeSchema = z
  .string()
  .trim()
  .superRefine((code, ctx) => {
    if (!code || code.length !== 9) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid PAC/STAC code'
      });
      return;
    }
    if (PAC_REGEX.test(code)) {
      return;
    }
    if (STAC_REGEX.test(code)) {
      return;
    }
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Please enter a valid PAC/STAC code'
    });
  });

export const ukPhoneNumberSchema = z
  .string()
  .trim()
  .transform((value) => value.replace(/[\s\-()]/g, ''))
  .refine(
    (value) => {
      return /^(07\d{9})$|^(\+?447\d{9})$/.test(value);
    },
    {
      message: 'Please enter a valid UK mobile number'
    }
  )
  .transform((value) => {
    if (value.startsWith('07')) {
      return `44${value.substring(1)}`;
    }
    if (value.startsWith('+44')) {
      return value.substring(1);
    }
    return value;
  });

// Function to check if a date is a valid switching date
// (not a weekend or UK public holiday)
export const isValidPortingDate = (date: Date): boolean => {
  // Check if it's a weekend
  const day = date.getDay();
  if (day === 0 || day === 6) {
    return false; // Weekend (Sunday = 0, Saturday = 6)
  }

  // Format date as YYYY-MM-DD for holiday checking
  const formattedDate = date.toISOString().split('T')[0];

  // Check if it's a public holiday
  return !UK_PUBLIC_HOLIDAYS.includes(formattedDate);
};

const isEmptyOrUndefined = (value: string | undefined): boolean => {
  return value === undefined || value === null || value === '';
};

// Complete form schema for number porting
export const numberPortingSchema = z
  .object({
    code: pacCodeSchema,
    incoming_phone_number: ukPhoneNumberSchema,
    // Switching date is optional but if provided must be valid
    // day: z.string().optional(),
    day: z.string({ invalid_type_error: 'Please select a day' }).optional(),
    // month: z.string().optional(),
    month: z.string({ invalid_type_error: 'Please select a month' }).optional(),
    // year: z.string().optional()
    year: z.string({ invalid_type_error: 'Please select a year' }).optional()
  })
  .superRefine((data, ctx) => {
    // Check if any date field has a non-empty value
    const hasDay = !isEmptyOrUndefined(data.day);
    const hasMonth = !isEmptyOrUndefined(data.month);
    const hasYear = !isEmptyOrUndefined(data.year);

    // Only validate date fields if at least one has a non-empty value
    const hasAnyDateField = hasDay || hasMonth || hasYear;

    if (hasAnyDateField) {
      // If any date field is provided with a value, all must be provided with values
      if (!hasDay) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Day is required when providing a switching date',
          path: ['day']
        });
      }

      if (!hasMonth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Month is required when providing a switching date',
          path: ['month']
        });
      }

      if (!hasYear) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Year is required when providing a switching date',
          path: ['year']
        });
      }

      // Only validate the date if all fields have values
      if (hasDay && hasMonth && hasYear) {
        try {
          const day = parseInt(data.day!, 10);
          const month = parseInt(data.month!, 10) - 1; // JS months are 0-indexed
          const year = parseInt(data.year!, 10);

          const date = new Date(year, month, day);

          // Check if the date is valid
          const isValidDate =
            date.getFullYear() === year &&
            date.getMonth() === month &&
            date.getDate() === day;

          if (!isValidDate) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Please select a valid date',
              path: ['day']
            });
          } else if (!isValidPortingDate(date)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message:
                'Please select a valid switching date (must be a business day)',
              path: ['day']
            });
          }
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Please select a valid date',
            path: ['day']
          });
        }
      }
    }
  });

export type NumberPortingFormData = z.infer<typeof numberPortingSchema>;

export const validateNumberPortingForm = (
  formData: unknown
): {
  success: boolean;
  data?: NumberPortingFormData;
  error?: z.ZodError;
} => {
  try {
    // Clean up empty string values for date fields
    if (typeof formData === 'object' && formData !== null) {
      const data = formData as Record<string, unknown>;

      // Convert empty strings to undefined for optional fields
      if (data.day === '') data.day = undefined;
      if (data.month === '') data.month = undefined;
      if (data.year === '') data.year = undefined;
    }

    const validatedData = numberPortingSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const emailSchema = z
  .string()
  .trim()
  .min(1, { message: 'Email address is required' })
  .email({ message: 'Please enter a valid email address' });

export type Email = z.infer<typeof emailSchema>;

export const validateEmailForm = (
  formData: unknown
): {
  success: boolean;
  data?: Email;
  error?: z.ZodError;
} => {
  try {
    const validatedData = emailSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

//
// export const signInFormSchema = z.object({
//   email: emailSchema
// });
//
// export type SignInFormData = z.infer<typeof signInFormSchema>;
//
// export const validateSignInForm = (
//   formData: unknown
// ): {
//   success: boolean;
//   data?: SignInFormData;
//   error?: z.ZodError;
// } => {
//   try {
//     const validatedData = signInFormSchema.parse(formData);
//     return { success: true, data: validatedData };
//   } catch (error) {
//     if (error instanceof z.ZodError) {
//       return { success: false, error };
//     }
//     throw error;
//   }
// };
//
// export const otpSchema = (length: number = 6) => {
//   return z
//     .string()
//     .transform((val) => val.trim())
//     .superRefine((val, ctx) => {
//       if (!val) {
//         ctx.addIssue({
//           code: z.ZodIssueCode.too_small,
//           minimum: 1,
//           type: 'string',
//           inclusive: true,
//           message: 'Please enter the verification code'
//         });
//         return;
//       }
//
//       const isNumeric = /^\d+$/.test(val);
//       if (!isNumeric) {
//         ctx.addIssue({
//           code: z.ZodIssueCode.invalid_string,
//           validation: 'regex',
//           message: 'The verification code must contain only numbers'
//         });
//       }
//
//       if (val.length !== length) {
//         ctx.addIssue({
//           code: z.ZodIssueCode.too_big,
//           maximum: length,
//           type: 'string',
//           inclusive: true,
//           message: `Please enter all ${length} digits of the verification code`
//         });
//       }
//     });
// };
