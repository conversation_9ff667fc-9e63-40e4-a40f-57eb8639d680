'use client';

import { useWizard } from '@/context/wizard/wizard';

export default function Registration() {
  const { goToNextStep, goBackToPreviousStep } = useWizard();
  return (
    <div className="flex h-full flex-col items-center justify-center p-4">
      <h1 className="mb-4 text-2xl font-bold">Registration</h1>
      <p>
        This is the Registration page. Content will be added here. - For uSwitch
        case FROM is not need. We are going to use SSO
      </p>
      <button onClick={goToNextStep}> Next</button>
      <button onClick={goBackToPreviousStep}>Back</button>
    </div>
  );
}
