import { AxiosClient } from '@/lib/axios-client';
import { API_ENDPOINTS } from '@/auth/api/endpoints';
import {
  SimsApiResponse,
  simsApiResponseSchema
} from '@/src/uswitch/schemas/schemas';

export const simService = {
  getSims: async (apiClient: AxiosClient): Promise<SimsApiResponse> => {
    const response = await apiClient.get(API_ENDPOINTS.sims.base);

    const result = simsApiResponseSchema.safeParse(response);

    if (!result.success) {
      console.error('Invalid sim data:', result.error.issues);
      throw new Error('Received invalid sim data from the server');
    }

    return result.data;
  },
  getPurchaseDetails: async (apiClient: AxiosClient): Promise<any> => {
    return await apiClient.get(API_ENDPOINTS.sims.purchaseDetails);
  }
};
