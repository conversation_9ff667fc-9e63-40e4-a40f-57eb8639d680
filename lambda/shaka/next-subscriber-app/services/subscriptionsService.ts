import { AxiosClient } from '@/lib/axios-client';
import { API_ENDPOINTS } from '@/auth/api/endpoints';
import {
  Email,
  SubscriptionsApiResponse,
  SubscriptionsSchema
} from '@/src/uswitch/schemas/schemas';

export const subscripionsService = {
  getSubscriptions: async (
    apiClient: AxiosClient
  ): Promise<SubscriptionsApiResponse> => {
    // return await apiClient.get(API_ENDPOINTS.plans.base);

    const response = await apiClient.get(API_ENDPOINTS.subscriptions.base);
    const result = SubscriptionsSchema.safeParse(response);

    if (!result.success) {
      console.error('Invalid plans data:', result.error.format());
      throw new Error('Received invalid plans data from the server');
    }

    return result.data;
  },
  getPurchaseDetails: async (apiClient: AxiosClient): Promise<any> => {
    return await apiClient.get(API_ENDPOINTS.plans.base);
  },
  shareEsim: async (apiClient: AxiosClient, email: Email): Promise<any> => {
    return await apiClient.post(API_ENDPOINTS.subscriptions.shareEsim(email));
  }
};
