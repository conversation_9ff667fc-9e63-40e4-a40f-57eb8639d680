"use client";
import { loadStripe, Stripe } from "@stripe/stripe-js";
import {
  PropsWithChildren,
  createContext,
  useContext,
} from "react";

export const StripeContext = createContext<{
  stripePromise: Promise<Stripe | null>;
}>({ stripePromise: new Promise(() => null) });

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PK || "", {
  betas: ["custom_checkout_beta_5"],
});

export const StripeProvider = ({ children }: PropsWithChildren) => {
  return (
    <StripeContext.Provider
      value={{
        stripePromise,
      }}
    >
      {children}
    </StripeContext.Provider>
  );
};

export const useStripe = () => {
  const context = useContext(StripeContext);
  if (!context) {
    throw new Error("useStripe must be used within an StripeProvider");
  }
  return context;
};
