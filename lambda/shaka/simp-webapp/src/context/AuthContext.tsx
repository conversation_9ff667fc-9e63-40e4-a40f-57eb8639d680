"use client";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { jwtDecode } from "jwt-decode";
import useLocalStorage, { LocalKey } from "@/hooks/useLocalStorage";
import { Tokens } from "@/types/auth";
import { getExpiresAt } from "@/helpers/auth";

type AuthData = Tokens & {
  expires_at: string;
};

export type AuthContextType = {
  login: (data: Tokens) => void;
  ssoLogin: (data: Partial<AuthData>) => void;
  logout: () => void;
  loggedIn: boolean;
  isLoading: boolean;
};

export const AuthContext = createContext<AuthContextType>({
  login: () => {},
  ssoLogin: () => {},
  logout: () => {},
  loggedIn: false,
  isLoading: true,
});

const AuthProvider = ({ children }: React.PropsWithChildren) => {
  const { setLSValues, removeLSValues } = useLocalStorage();

  const [loggedIn, setLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const validateAuthTokens = ({
    id_token,
    access_token,
    refresh_token,
  }: Tokens): AuthData | null => {
    const userData = jwtDecode(id_token);

    if ("email" in userData && "cognito:username" in userData) {
      const expiresAt = getExpiresAt(access_token);

      return { id_token, access_token, refresh_token, expires_at: expiresAt };
    }

    return null;
  };

  const setSubscriberSession = (
    idToken: string,
    accessToken: string,
    expiresAt: string,
    refresh_token: string,
  ) => {
    setLSValues([
      { key: LocalKey.ACCESS_TOKEN, value: accessToken },
      { key: LocalKey.EXPIRES_AT, value: expiresAt },
      { key: LocalKey.ID_TOKEN, value: idToken },
      { key: LocalKey.REFRESH_TOKEN, value: refresh_token },
    ]);
  };

  const logout = useCallback(() => {
    removeLSValues([
      LocalKey.ACCESS_TOKEN,
      LocalKey.EXPIRES_AT,
      LocalKey.ID_TOKEN,
      LocalKey.REFRESH_TOKEN,
    ]);
    // console.log('logging out');
    setLoggedIn(false);
  }, [removeLSValues]);

  const login = (tokens: Tokens) => {
    // console.log('logging');
    const validatedData = validateAuthTokens(tokens);
    // console.log('vdg', validatedData);

    if (!validatedData) {
      // console.log('not validated');
      logout();
      return;
    }

    setSubscriberSession(
      validatedData.id_token,
      validatedData.access_token,
      validatedData.expires_at,
      validatedData.refresh_token,
    );
    // console.log("setting");
    setLoggedIn(true);
    // console.log("set");
  };

  const ssoLogin = ({
    expires_at = "",
    access_token = "",
  }: Partial<AuthData>) => {
    setLSValues([
      { key: LocalKey.ACCESS_TOKEN, value: access_token },
      { key: LocalKey.EXPIRES_AT, value: expires_at },
    ]);
    setLoggedIn(true);
  };

  useEffect(() => {
    const token = localStorage.getItem(LocalKey.ACCESS_TOKEN);

    if (!token) {
      logout();
    } else {
      setLoggedIn(true);
    }
    setIsLoading(false);
  }, [logout]);

  // console.log('Provider, returning', login);

  return (
    <AuthContext.Provider
      value={{ login, ssoLogin, logout, loggedIn, isLoading }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
