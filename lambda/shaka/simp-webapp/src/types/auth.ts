export type TokensResponse = {
  AccessToken: string;
  IdToken: string;
  RefreshToken: string;
}

export type Tokens = {
  id_token: string;
  access_token: string;
  refresh_token: string;
};

export type SignupForm = {
  name: string;
  email: string;
  password: string;
  date?: string;
};

export type SignupPayload = {
  email: string;
  password: string;
  name: string;
  date_of_birth: string;
  plan_id: number;
  is_underage: boolean;
};

export type LoginData = {
  email: string;
  password: string;
};

export type LoginSsoPayload = {
  code: string;
};
