import api from "./index";

type Branding = {
  id: number;
  client: number;
  logo: string;
  headings_font: number;
  paragraph_font: number;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  text_color: string;
  link_color: string;
};

type Plan = {
  id: number;
  bg: number;
  title: string;
  data: string;
  voice: string;
  sms: string;
  price: string;
  current_price: string;
  active: boolean;
};

type SsoLinks = {
  google: string;
  facebook: string;
  apple: string;
};

type Client = {
  id: number;
  name: string;
  branding: Branding;
  perk_point_name_singular: string;
  perk_point_name_plural: string;
  plans: Plan[];
  spn: string;
  sso_links: SsoLinks;
};

export const fetchClient = (): Promise<Client> =>
  api.get(`/data/client/`).then((res) => res.data);
