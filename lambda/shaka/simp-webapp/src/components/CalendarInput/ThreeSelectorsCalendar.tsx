import { Fragment, useState } from "react";
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from "@headlessui/react";
import clsx from "clsx";
import { FieldName, Input } from "./types";
import {
  daysOptions,
  getDefaultValue,
  monthsOptions,
  yearsOptions,
} from "./helpers";
import { twMerge } from "tailwind-merge";
import InputWrapper, { InputWrapperProps } from "../InputWrapper";
import Image from "next/image";

interface Props {
  value?: string;
  onChange?: (value: string) => void;
}

export default function ThreeSelectorsCalendar({
  value,
  onChange,
  ...blockProps
}: Props & InputWrapperProps) {
  const [defaultYear, defaultMonth, defaultDay] = value?.split("-") || [];

  const [focusedInput, setFocusedInput] = useState<FieldName | null>(null);

  const [year, setYear] = useState<Input>(
    getDefaultValue(defaultYear, yearsOptions),
  );
  const [month, setMonth] = useState<Input>(
    getDefaultValue(defaultMonth, monthsOptions),
  );
  const [day, setDay] = useState<Input>(
    getDefaultValue(defaultDay, daysOptions),
  );

  type Field = {
    name: FieldName;
    fieldValue: Input;
    setValue: React.Dispatch<React.SetStateAction<Input>>;
    options: Input[];
    defaultValue: string;
  }[];

  const fields: Field = [
    {
      name: "day",
      fieldValue: day,
      setValue: setDay,
      options: daysOptions,
      defaultValue: "Day",
    },
    {
      name: "month",
      fieldValue: month,
      setValue: setMonth,
      options: monthsOptions,
      defaultValue: "Month",
    },
    {
      name: "year",
      fieldValue: year,
      setValue: setYear,
      options: yearsOptions,
      defaultValue: "Year",
    },
  ];

  const handleFieldsChange = (name: FieldName) => (value: Input) => {
    setFocusedInput(null);
    const newValue = value?.data || "";
    let currentYear = year?.data || "";
    let currentMonth = month?.data || "";
    let currentDay = day?.data || "";
    setFocusedInput(null);

    switch (name) {
      case "day":
        setDay(value);
        currentDay = newValue;
        break;
      case "month":
        setMonth(value);
        currentMonth = newValue;
        break;
      case "year":
        setYear(value);
        currentYear = newValue;
        break;
      default:
        break;
    }

    const formattedDate = [currentYear, currentMonth, currentDay]
      .filter(Boolean)
      .map((v) => v.toString().padStart(2, "0"))
      .join("-");

    onChange?.(formattedDate);
  };

  const handleFocusChange = (name: FieldName) => () => {
    setFocusedInput(name);
  };

  return (
    <InputWrapper {...blockProps}>
      <div className="flex gap-2">
        {fields.map(({ fieldValue, options, defaultValue, name }) => (
          <div
            className={clsx("relative", name === "day" ? "w-[30%]" : "w-[35%]")}
            key={name}
          >
            <Listbox value={fieldValue} onChange={handleFieldsChange(name)}>
              <ListboxButton
                className={twMerge(
                  "bg-[#ffffff1a] w-full py-4 pb-3 px-2 rounded-lg text-base focus:outline-none pr-8 border border-transparent",
                  focusedInput === name && "focused border-[#FDFE00]",
                )}
                onClick={handleFocusChange(name)}
              >
                {fieldValue ? (
                  <span className="block truncate">
                    {fieldValue.label || fieldValue.data}
                  </span>
                ) : (
                  <span className="block truncate text-[#868686] font-normal">
                    {defaultValue}
                  </span>
                )}

                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <Image
                    src="/icons/chevron-down.svg"
                    width={12}
                    height={10}
                    alt="chevron-down"
                  />
                </span>
              </ListboxButton>

              <ListboxOptions className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-[#4a4a4a] py-1 text-sm z-10">
                {options.map((option) => (
                  <ListboxOption
                    key={option?.data}
                    as={Fragment}
                    value={option}
                  >
                    {({ selected }) => (
                      <div
                        className={`option relative cursor-pointer select-none py-2 px-3 hover:bg-[#ffffff1a] ${
                          selected ? "bg-[#ffffff1a]" : "text-white/60"
                        }`}
                      >
                        <span
                          className={`block truncate ${
                            selected ? "font-medium" : "font-normal"
                          }`}
                        >
                          {option?.label || option?.data}
                        </span>
                        {selected ? (
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600"></span>
                        ) : null}
                      </div>
                    )}
                  </ListboxOption>
                ))}
              </ListboxOptions>
            </Listbox>
          </div>
        ))}
      </div>
    </InputWrapper>
  );
}
