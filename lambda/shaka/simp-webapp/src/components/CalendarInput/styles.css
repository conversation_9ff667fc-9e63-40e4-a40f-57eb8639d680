.calendar-placeholder {
    inset: 0;
    position: absolute;
    background: white;
    color: #9ca3af;
    pointer-events: none;
    z-index: 10;
    padding-top: 8px;
}

.input .react-date-picker__wrapper {
    border: 0;
    display: flex;
    justify-content: space-between;
}

.react-date-picker__calendar.react-date-picker__calendar--open {
    margin-top: 4px;
}

.react-calendar__decade-view__years__year:disabled,
.react-calendar__year-view__months__month:disabled,
.react-calendar__month-view__days__day:disabled {
    opacity: 0.5;
}

.react-date-picker__inputGroup {
    flex-grow: 1;
}

.input .react-date-picker__inputGroup__input {
    outline: none;
}

.input .react-date-picker__inputGroup__input:invalid {
    background-color: transparent;
}

.calendar.react-calendar {
    background: white;
    border-radius: 8px;
    padding: 16px;
    z-index: 100;
    border: 1px solid #ffffff;
}

.react-calendar abbr:where([title]) {
    font-weight: 600;
    text-decoration: none;
}

.react-calendar__navigation {
    margin-bottom: 16px;
    display: flex;
    gap: 4px;
}

.react-calendar__navigation__arrow {
    background: #0000001a;
    border: none;
    border-radius: 50%;
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    width: 32px;
}