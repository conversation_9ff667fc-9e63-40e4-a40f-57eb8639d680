import { PropsWithChildren, ReactNode } from "react";
import ErrorText from "./ErrorText";
import Image from "next/image";

export type InputWrapperProps = {
  name: string;
  title?: string;
  error?: string;
  description?: ReactNode;
  errorAbsolute?: boolean;
  withValidationMark?: boolean;
  hideErrorMessage?: boolean;
};

export default function InputWrapper({
  children,
  name,
  title,
  error,
  description,
  errorAbsolute,
  withValidationMark,
  hideErrorMessage,
}: PropsWithChildren<InputWrapperProps>) {
  return (
    <div className="flex flex-col gap-1 relative w-full">
      {title && (
        <label htmlFor={name} className="text-sm">
          {title}
        </label>
      )}
      {children}

      {withValidationMark && (
        <span className="absolute top-11 right-4">
          <Image
            src="/icons/check-yellow.svg"
            alt="Check Icon"
            width={12}
            height={12}
          />
        </span>
      )}

      {description}

      {!hideErrorMessage && (
        <ErrorText size="sm" absolute={errorAbsolute}>
          {error}
        </ErrorText>
      )}
    </div>
  );
}
