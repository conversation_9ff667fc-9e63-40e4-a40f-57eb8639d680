const featuresList = [
  "unlimited data",
  "unlimited calls & texts",
  "free global roaming",
  "weekly rewards",
  "superfast 5G",
];

export default function FeaturesList() {
  return (
    <div className="flex flex-wrap justify-between px-4 mb-[6%] mt-12 max-md:space-y-8">
      {featuresList.map((feature) => (
        <span
          key={feature}
          className="uppercase w-full md:w-auto text-center md:text-left font-semibold"
        >
          {feature}
        </span>
      ))}
    </div>
  );
}
