import React, { useContext, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { AuthContext } from "@/context/AuthContext";
import Loading from "@/components/Loading";
import useLocalStorage, { LocalKey } from "@/hooks/useLocalStorage";

const withAuth = <P extends object>(
  WrappedComponent: React.ComponentType<P>
): React.FC<P> => {
  const RequiresAuth: React.FC<P> = (props) => {
    const { loggedIn, isLoading } = useContext(AuthContext);
    const pathname = usePathname();
    const router = useRouter();
    const { getLSValue, removeLSValue } = useLocalStorage(LocalKey.REDIRECT);

    useEffect(() => {
      if (!isLoading && !loggedIn) {
        const redirect = getLSValue();
        removeLSValue();

        if (redirect) {
          router.replace(redirect);
          return;
        }

        const encodedPathname = encodeURIComponent(pathname);
        router.replace("/login?redirect=" + encodedPathname);
      }
    }, [getLSValue, isLoading, loggedIn, pathname, removeLSValue, router]);

    if (loggedIn) {
      return <WrappedComponent {...props} />;
    }

    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loading />
      </div>
    );
  };

  return RequiresAuth;
};

export default withAuth;
