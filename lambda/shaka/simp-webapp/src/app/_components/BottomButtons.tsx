"use client";

import LoginButton from "@/components/LoginButton";
import SignUpButton from "@/components/SignUpButton";
import AuthProvider, { useAuth } from "@/context/AuthContext";

function Buttons() {
  const { isLoading, loggedIn } = useAuth();

  if (isLoading || loggedIn) {
    return null;
  }

  return (
    <nav className="flex flex-col gap-4 md:hidden mb-10">
      <SignUpButton />
      <LoginButton />
    </nav>
  );
}

export default function BottomButtons() {
  return (
    <AuthProvider>
      <Buttons />
    </AuthProvider>
  );
}
