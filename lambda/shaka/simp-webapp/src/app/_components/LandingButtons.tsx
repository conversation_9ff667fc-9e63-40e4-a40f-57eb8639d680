"use client";
import SignUpButton from "@/components/SignUpButton";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import Link from "next/link";
import { Button } from "@/components/Button";
import { Navigation } from "@/app/navigation";
import { useSubscriber } from "@/hooks/useSubscriber";

function LandingButtons() {
  const { isLoading, loggedIn } = useAuth();
  const { subscriber } = useSubscriber();

  if (isLoading) {
    return <div className="h-[52px]" />;
  }

  return (
    <div className="w-full md:w-2/3">
      {loggedIn && (
        <Link href={Navigation.DASHBOARD}>
          {subscriber?.plans && (
            <Button variant="filled-gray">Back home</Button>
          )}
          {(!subscriber?.plans || !subscriber.is_verified) && (
            <Button variant="filled-gray">Finish signing up</Button>
          )}
        </Link>
      )}

      {!loggedIn && <SignUpButton />}
    </div>
  );
}

export default function Buttons() {
  return (
    <AuthProvider>
      <LandingButtons />
    </AuthProvider>
  );
}
