"use client";
import { Suspense, useActionState, useState } from "react";
import Input from "@/components/Input";
import { Button } from "@/components/Button";
import { confirmForgotPassword } from "@/actions/forgot-password";
import { ConfirmForgotPasswordInputs } from "@/schemas/forgot-password";
import ErrorText from "@/components/ErrorText";
import { useSearchParams } from "next/navigation";
import Link from "next/link";

const ForgotPasswordPage = () => {
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  const [confirmPasswordState, confirmPasswordAction, confirmPasswordPending] =
    useActionState(confirmForgotPassword, undefined);

  const [formData, setFormData] = useState<ConfirmForgotPasswordInputs>({
    email: email || "",
    new_password: "",
    new_password_confirmation: "",
    verification_code: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4 text-center">Reset password</h1>
      <p className="text-center text-[#868686] mb-4">
        Enter the verification code and your new password.
      </p>
      <form
        action={confirmPasswordAction}
        className="flex flex-col gap-4 max-w-md m-auto"
      >
        <Input
          name="verification_code"
          placeholder="Verification code"
          value={formData.verification_code}
          onChange={handleChange}
          error={confirmPasswordState?.errors?.verification_code?.[0]}
        />
        <Input
          name="email"
          type="email"
          placeholder="Email"
          value={formData.email}
          onChange={handleChange}
          error={confirmPasswordState?.errors?.email?.[0]}
        />
        <Input
          name="new_password"
          type="password"
          placeholder="New password"
          value={formData.new_password}
          onChange={handleChange}
          error={confirmPasswordState?.errors?.new_password?.[0]}
        />
        <Input
          name="new_password_confirmation"
          type="password"
          placeholder="Confirm new password"
          value={formData.new_password_confirmation}
          onChange={handleChange}
          error={confirmPasswordState?.errors?.new_password_confirmation?.[0]}
        />

        <ErrorText>{confirmPasswordState?.formError}</ErrorText>

        <Button
          type="submit"
          disabled={confirmPasswordPending}
          variant="filled"
        >
          Submit
        </Button>
        <Link href="/login">
          <Button
            type="button"
            disabled={confirmPasswordPending}
            variant="outlined"
          >
            Go to login
          </Button>
        </Link>
      </form>
    </div>
  );
};

export default function ForgotPasswordPageWrapped() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ForgotPasswordPage />
    </Suspense>
  );
}
