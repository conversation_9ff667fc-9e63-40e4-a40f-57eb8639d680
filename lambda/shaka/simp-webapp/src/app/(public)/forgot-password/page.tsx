"use client";
import { useActionState, useState } from "react";
import Input from "@/components/Input";
import { Button } from "@/components/Button";
import { forgotPassword } from "@/actions/forgot-password";
import { ForgotPasswordInputs } from "@/schemas/forgot-password";
import ErrorText from "@/components/ErrorText";
import Link from "next/link";
import { Navigation } from "@/app/navigation";

const ForgotPasswordPage = () => {
  const [formData, setFormData] = useState<ForgotPasswordInputs>({
    email: "",
  });

  const [forgotPasswordState, forgotPasswordAction, forgotPasswordPending] =
    useActionState(forgotPassword, undefined);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4 text-center">Forgot password?</h1>
      <p className="text-center text-[#868686] mb-4">
        Enter your email address and we will send you a link to reset your
        password.
      </p>
      <form
        action={forgotPasswordAction}
        className="flex flex-col gap-4 max-w-md m-auto"
      >
        <Input
          name="email"
          type="email"
          placeholder="Email"
          value={formData.email}
          onChange={handleChange}
          error={forgotPasswordState?.errors?.email?.[0]}
        />

        <ErrorText>{forgotPasswordState?.formError}</ErrorText>

        <div className="flex flex-col gap-4 md:flex-row md:justify-between">
          <Link href={Navigation.LOGIN} className="w-full">
            <Button
              type="button"
              disabled={forgotPasswordPending}
              variant="filled-gray"
            >
              Return
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={forgotPasswordPending}
            variant="filled"
          >
            Send link
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ForgotPasswordPage;
