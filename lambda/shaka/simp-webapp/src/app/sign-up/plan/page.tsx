"use client";

import { But<PERSON> } from "@/components/Button";
import Link from "next/link";
import Image from "next/image";
import SignUpContent from "@/components/SignUpContent";
import FeaturesCard from "@/components/FeaturesCard";
import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogBackdrop,
  DialogPanel,
} from "@headlessui/react";
import { androidModels, iPhoneModels } from "@/app/sign-up/plan/_constants";
import {
  CheckMarkRedBg,
  CheckMarkWhiteBg,
  GenericCard,
} from "@/app/sign-up/plan/_components/generic-card";

export default function SignUpPlanPage() {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <SignUpContent biggerRight stepIndex={1}>
        <SignUpContent.Left
          title={
            <>
              <span className="font-bold">Just Unlimited</span>{" "}
              <span className="font-medium">Plan</span>
            </>
          }
          description={
            <p>
              One plan with a tonne of great benefits. Don’t forget - we are{" "}
              <button onClick={() => setIsOpen(true)} className="underline">
                eSIM only
              </button>
              .
            </p>
          }
          actionButton={
            <Link href="/sign-up/" className="w-[300px]">
              <Button variant="filled">I am sold!</Button>
            </Link>
          }
        />
        <SignUpContent.Right>
          <>
            <div className="relative pt-36 md:pt-0 w-[90%] md:w-full max-md:m-auto md:ml-auto md:mb-16 md:max-w-lg md:-mt-6">
              <div className="flex justify-center w-full max-md:absolute bottom-[240px] left-0 translate-y-4 md:translate-y-20 m-auto">
                <Image
                  src="/images/floating-plan-new.png"
                  alt="Plan"
                  width={486}
                  height={330}
                  className="max-md:max-w-[350px] ml-4"
                />
              </div>
              <FeaturesCard />
            </div>
            <div className="md:hidden fixed bottom-4 w-[90%]">
              <Link href="/sign-up/">
                <Button variant="filled">Oh wow, great value for money!</Button>
              </Link>
            </div>
          </>
        </SignUpContent.Right>
      </SignUpContent>
      <Dialog
        open={isOpen}
        className="relative z-10 focus:outline-none"
        onClose={() => setIsOpen(false)}
      >
        <DialogBackdrop className="fixed inset-0 bg-black/30 backdrop-blur-sm" />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <DialogPanel
              transition
              className="w-full max-w-[750px] h-[calc(100dvh_-_40px)] md:h-auto rounded-xl bg-[#252525]/80 pt-8 px-7 md:p-12 backdrop-blur-[20px] relative overflow-hidden"
            >
              <button
                className="bg-[#D9D9D9]/20 rounded-full size-7 flex justify-center items-center absolute top-5 right-5"
                onClick={() => setIsOpen(false)}
              >
                <Image
                  src="/icons/cross.svg"
                  width={12}
                  height={12}
                  alt="close-icon"
                />
              </button>

              <DialogTitle
                as="h3"
                className="text-[24px] md:text-[29px] w-full"
                // className="text-[24px] md:text-[29px] max-w-[200px] md:max-w-full md:w-full"
              >
                Make sure your phone can handle our eSIM! 🥵
              </DialogTitle>
              <div className="overflow-y-auto overflow-x-hidden mt-2 h-[calc(100%_-_180px)] md:h-auto">
                <p className="text-white text-[16px] md:text-[20px] max-w-[280px] md:max-w-full mt-2 md:mt-3">
                  Essentially all iPhones today are eSIM enabled. Pay more
                  attention to the compatibility list if you are on Android.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-6">
                  <GenericCard
                    phoneType="APPLE"
                    phoneIconSrc="/icons/apple3dIcon.png"
                    header="Compatible iPhones"
                    description={
                      <p className="text-[#868686] text-[12.8px]">
                        If you bought your iPhone in the last 6<br /> years, you
                        are good!
                      </p>
                    }
                  >
                    <ul className="text-[13px] space-y-1 font-semibold pt-4">
                      {iPhoneModels.map((phone, index) => (
                        <li key={index} className="flex gap-2 items-center">
                          {phone.isIncompatible ? (
                            <CheckMarkRedBg />
                          ) : (
                            <CheckMarkWhiteBg />
                          )}
                          <p className="pt-1">{phone.model}</p>
                        </li>
                      ))}
                    </ul>
                  </GenericCard>
                  <GenericCard
                    phoneType="ANDROID"
                    phoneIconSrc="/icons/android3dIcon.png"
                    header="Compatible Androids"
                    description={
                      <p className="text-[#868686] text-[12.8px]">
                        Generally all Google Pixels and most
                        <br /> Samsungs are compatible
                      </p>
                    }
                  >
                    <ul className="text-[13px] font-semibold pt-4">
                      {androidModels.map((phone, index) => (
                        <li key={index} className="flex gap-2">
                          <CheckMarkWhiteBg className="flex-shrink-0 mt-[0.3rem]" />
                          <p className="pt-1">{phone.model}</p>
                        </li>
                      ))}
                    </ul>
                  </GenericCard>
                </div>
              </div>
              <Button
                onClick={() => setIsOpen(false)}
                variant="filled"
                className="md:w-full md:col-span-2 mt-4 md:mt-8"
              >
                Continue to sign up
              </Button>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  );
}
