"use client";

import { epochToDate, getHumanReadableDateFormat } from "@/helpers/dates";
import { currencyRounded } from "@/helpers/numbers";
import withAuth from "@/hoc/withAuth";
import { useSubscriber } from "@/hooks/useSubscriber";
import Image from "next/image";
import { twMerge } from "tailwind-merge";
import PaymentDetailsDialog from "./_components/PaymentDetailsDialog";
import CancelSubscriptionDialog from "./_components/CancelSubscriptionDialog";
import PopUp from "./_components/PopUp";
import FeaturesCard from "@/components/FeaturesCard";
import { Subscriber } from "@/types/subscriber";
import { Button } from "@/components/Button";
import { cancelSubscriptionUpdate } from "@/api/subscription";
import LoginButtonsWrapper from "@/components/LoginButtons";
import Footer from "@/components/Footer";
import useFlowRedirect from "@/hooks/useFlowRedirect";
import useChat from "@/hooks/useChat";

function Dashboard() {
  useChat();
  const { subscriber, updateSubscriber } = useSubscriber();
  useFlowRedirect({ subscriber, fetchSubscriberOnRender: false });

  const plan = subscriber?.plans?.[0] || ({} as Subscriber["plans"][0]);
  const {
    next_bill_amount: price = 0,
    next_bill_date_epoch: billDateEpoch = 0,
    latest_plan_change: latestPlanChange,
    can_cancel: canCancelPlan,
  } = plan;

  const billDate = epochToDate(billDateEpoch);
  const humanReadableDate = getHumanReadableDateFormat(billDate);
  const isPlanCancelled =
    latestPlanChange && latestPlanChange.change_type === "cancellation";

  const handleRevertCancellation = () => {
    cancelSubscriptionUpdate(latestPlanChange!.id).then(() => {
      updateSubscriber();
    });
  };

  return (
    <>
      <div className="grow">
        <LoginButtonsWrapper withLogout />
        <PopUp />
        <div className="flex gap-[10vw] md:gap-20 flex-col lg:flex-row md:mt-20 md:justify-between">
          <PlanInfoCard subscriber={subscriber} />
          <BillingInfoCard
            isPlanCancelled={isPlanCancelled}
            humanReadableDate={humanReadableDate}
            price={price}
            lastFourCardDigits={subscriber?.last_4_digits}
            onRevertCancellation={handleRevertCancellation}
            canCancelPlan={canCancelPlan}
          />
        </div>
      </div>
      <Footer />
    </>
  );
}

export default withAuth(Dashboard);

function PlanInfoCard({ subscriber }: { subscriber: Subscriber | undefined }) {
  return (
    <div className="relative pt-4 md:pt-0 md:max-w-lg">
      <p className="text-3xl font-semibold whitespace-nowrap">Your plan:</p>
      <p className="text-base leading-6 mb-6 md:mb-0 mt-6 text-[#CFCFCF] md:text-white md:text-xl md:leading-8 md:mt-4">
        {subscriber && `${subscriber?.name} (${subscriber?.email})`} &nbsp;{" "}
      </p>
      <div className="w-[90%] md:w-full max-md:m-auto">
        <div className="flex justify-center w-full translate-y-12 md:translate-y-20 m-auto max-w-[85%] -mt-12 md:-mt-10">
          <Image
            src="/images/floating-plan-new.png"
            alt="Plan"
            width={486}
            height={330}
          />
        </div>
        <FeaturesCard />
      </div>
    </div>
  );
}

function BillingInfoCard({
  isPlanCancelled,
  humanReadableDate,
  price,
  lastFourCardDigits,
  onRevertCancellation,
  canCancelPlan,
}: {
  isPlanCancelled: boolean | null;
  humanReadableDate: string;
  price: number | null | string;
  lastFourCardDigits: string | undefined;
  onRevertCancellation: () => void;
  canCancelPlan: boolean;
}) {
  return (
    <div className="text-left md:w-2/5">
      <p className="text-3xl font-semibold whitespace-nowrap">Billing:</p>
      <div className="text-base leading-6 mt-6 text-[#CFCFCF] md:text-white md:text-xl md:leading-8 md:mt-12 space-y-4">
        {isPlanCancelled ? (
          <p>Your plan is due to terminate on the {humanReadableDate}</p>
        ) : (
          <>
            <p>
              Your next bill will be{" "}
              <span className="font-semibold">
                {currencyRounded(Number(price))}
              </span>{" "}
              and taken on the{" "}
              <span className="font-semibold">{humanReadableDate}</span>.
            </p>
            <PaymentMethodInfo lastFourCardDigits={lastFourCardDigits} />
          </>
        )}
      </div>
      <div
        className={twMerge("flex flex-col mt-10 md:flex-row md:mt-16", "gap-4")}
      >
        {isPlanCancelled ? (
          <Button
            onClick={onRevertCancellation}
            variant="filled"
            className="w-full md:w-auto"
          >
            Revert cancellation
          </Button>
        ) : (
          <>
            <PaymentDetailsDialog />
            {canCancelPlan && <CancelSubscriptionDialog />}
          </>
        )}
      </div>
    </div>
  );
}

function PaymentMethodInfo({
  lastFourCardDigits,
}: {
  lastFourCardDigits: string | undefined;
}) {
  return (
    <>
      {lastFourCardDigits ? (
        <p>
          We&apos;ll take the payment from card ending **** {lastFourCardDigits}
          .
        </p>
      ) : (
        <p>We&apos;ll attempt to take the payment automatically.</p>
      )}
    </>
  );
}
