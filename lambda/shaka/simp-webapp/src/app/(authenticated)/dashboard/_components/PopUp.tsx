import { Navigation } from "@/app/navigation";
import Link from "next/link";
import Image from "next/image";

export default function PopUp() {
  return (
    <Link
      href={Navigation.APP_SETTINGS}
      className="bg-[#D9D9D91A] rounded-[20px] font-semibold md:absolute top-[112px] left-1/2 md:-translate-x-1/2 md:-translate-y-1/2 py-3 px-7 flex gap-4 items-center max-md:mb-5"
    >
      <p className="mt-1">
        Are you looking to do anything else? You need to go to{" "}
        <span className="underline text-[#FDFE00]">the app</span>
      </p>
      <Image
        quality={100}
        src="/images/small-logo.png"
        alt="app logo"
        width={32}
        height={32}
      />
    </Link>
  );
}
