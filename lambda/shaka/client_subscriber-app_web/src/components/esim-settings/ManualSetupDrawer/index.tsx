import { Tab } from '@headlessui/react';
import { useState } from 'react';
import withDrawer from 'src/hocs/withDrawer';
import { twMerge } from 'tailwind-merge';
import CopyArea from '../common/CopyArea';
import { SimSettings } from 'src/types/sim-settings';

const getTabStyles = (selected: boolean) =>
  twMerge(
    'w-1/2 py-3.5 font-bold rounded-t-xl outline-none',
    selected ? 'bg-white text-black' : 'bg-[#EBEBEB] text-gray-500'
  );

const ManualSetupDrawerContent = ({
  esimSettings = {} as SimSettings
}: {
  esimSettings?: SimSettings;
}) => {
  const [selectedTab, setSelectedTab] = useState(0);

  const { android_settings, ios_settings } = esimSettings;

  return (
    <div className="h-[70vh] px-5 text-black">
      <p className="text-[#858585] font-semibold text-sm mb-4">
        Select your Phone Type
      </p>

      <div className="bg-[#EBEBEB] rounded-xl">
        <Tab.Group onChange={setSelectedTab}>
          <Tab.List className="flex relative text-sm">
            <Tab className={({ selected }) => getTabStyles(selected)}>
              Android
            </Tab>
            <Tab className={({ selected }) => getTabStyles(selected)}>
              iPhone
            </Tab>
            <div
              className={twMerge(
                'bg-white w-4 absolute inset-y-0 left-1/2 pointer-events-none',
                selectedTab === 0 ? 'translate-x-0' : '-translate-x-full'
              )}
            />
            <div
              className={twMerge(
                'bg-[#EBEBEB] rounded-bl-xl w-4 absolute inset-y-0 left-1/2 pointer-events-none',
                selectedTab === 0
                  ? 'translate-x-0'
                  : '-translate-x-full -scale-x-[1]'
              )}
            />
          </Tab.List>

          <Tab.Panels
            className={twMerge(
              'p-4 bg-white rounded-b-xl text-sm',
              selectedTab === 0 ? 'rounded-tr-xl' : 'rounded-tl-xl'
            )}
          >
            {/* Android Panel */}
            <Tab.Panel>
              <p className="font-semibold mt-2 mb-3">
                Copy the following code by clicking on the field below:
              </p>
              <CopyArea
                title={android_settings.sm_dp_activation}
                label="SM-DP ADDRESS & ACTIVATION CODE"
                layout="vertical"
              />
              <p className="font-semibold mt-8 mb-3">
                In your settings head to:
              </p>
              <p className="italic text-xs">
                Connections &gt; SIM Card Manager &gt; Tap on “Add Mobile Plan”
                &gt; “Scan Carrier QR Code” then tap on “Enter Code Manually”
              </p>

              <p className="font-semibold mt-3 mb-1">
                Paste the copied code and confirm.
              </p>
            </Tab.Panel>

            {/* iPhone Panel */}
            <Tab.Panel className="overflow-y-auto h-[calc(70vh_-_118px)]">
              <h3 className="text-xl font-bold">To install your eSIM</h3>
              <p className="font-semibold mt-2 mb-3">
                Copy the following codes by clicking on the field below:
              </p>

              <div className="space-y-2">
                <CopyArea title={ios_settings.sm_dp} label="SM-DP+ Address" />
                <CopyArea
                  title={ios_settings.activation_code}
                  label="Activation Code"
                />
              </div>

              <p className="font-semibold mt-3 mb-3">
                In your settings head to:
              </p>
              <p className="italic text-xs">
                Mobile Service &gt; Add eSIM &gt; Use QR Code &gt; Enter Details
                Manually
              </p>

              <p className="font-semibold mt-3 mb-1">
                Paste the copied code and confirm.
              </p>

              <h3 className="text-xl font-bold mt-6">To set up Data</h3>
              <p className="font-semibold mt-2 mb-3">
                Copy the following code by clicking on the field below:
              </p>

              <span className="text-[#727985] text-xs font-semibold mb-1">
                Mobile Data APN
              </span>
              <CopyArea title="gamma" titleBold />

              <p className="font-semibold mt-3 mb-3">
                In your settings head to:
              </p>
              <p className="italic text-xs">
                Mobile Service &gt; Click this eSIM &gt; Mobile Data Network
              </p>

              <p className="font-semibold mt-3 mb-1">
                Paste the copied code and confirm.
              </p>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
};

const ManualSetupDrawerDrawer = withDrawer(ManualSetupDrawerContent);

export default ManualSetupDrawerDrawer;
