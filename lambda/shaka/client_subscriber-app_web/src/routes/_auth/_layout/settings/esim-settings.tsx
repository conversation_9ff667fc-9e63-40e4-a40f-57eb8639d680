import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import { EsimSettings as EsimSettingsContent } from 'src/components/esim-settings';
import EsimPlanDropdown from 'src/components/EsimPlanDropdown';
import useSubscription from 'src/hooks/useSubscription';
import { SubscriberPlanSimItem } from 'src/context/SubscriptionContext.tsx';

export const Route = createFileRoute('/_auth/_layout/settings/esim-settings')({
  component: EsimSettings
});

function EsimSettings() {
  const { subscriberPlanSimList, currentPlanIndex } = useSubscription();
  const subscriberPlanSim = subscriberPlanSimList[currentPlanIndex];

  const [selectedPlan, setSelectedPlan] =
    useState<SubscriberPlanSimItem | null>(subscriberPlanSim);

  const handlePlanChange = (plan: SubscriberPlanSimItem) => {
    setSelectedPlan(plan);
  };

  return (
    <div className="pb-10">
      <PageTitle withBottomMargin={false}>Esim instructions</PageTitle>
      <div className="text-center mb-4">
        <EsimPlanDropdown
          selectedPlan={selectedPlan}
          onChange={handlePlanChange}
        />
      </div>
      <EsimSettingsContent plan={selectedPlan || undefined} />
    </div>
  );
}
